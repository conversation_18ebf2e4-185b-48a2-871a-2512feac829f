<?php
// Тестов скрипт за контролера на промоции
// Дата: 2025-07-08

// Включване на конфигурацията
require_once('admin/config.php');

// Включване на autoloader-а
require_once(THEME_AUTOLOADER);

echo "<h2>Тест на контролера за промоции</h2>";

try {
    // Симулираме registry
    $registry = new Registry();
    
    // Създаваме DB връзка
    $db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
    $registry->set('db', $db);
    
    // Създаваме config
    $config = new Config();
    $registry->set('config', $config);
    
    // Създаваме request
    $request = new Request();
    $registry->set('request', $request);
    
    // Създаваме response
    $response = new Response();
    $registry->set('response', $response);
    
    echo "✅ Registry е създаден успешно<br>";
    
    // Тестваме създаването на суб-контролера
    $promotionController = new \Theme25\Backend\Controller\Catalog\Product\Promotion($registry);
    echo "✅ Promotion контролерът е създаден успешно<br>";
    
    // Тестваме метода за създаване на таблицата
    $reflection = new ReflectionClass($promotionController);
    $createTableMethod = $reflection->getMethod('createPromotionsTable');
    $createTableMethod->setAccessible(true);
    
    echo "Създаваме таблицата чрез контролера...<br>";
    $createTableMethod->invoke($promotionController);
    echo "✅ Таблицата е създадена чрез контролера<br>";
    
    // Тестваме метода за получаване на промоции
    $getAllPromotionsMethod = $reflection->getMethod('getAllPromotions');
    $getAllPromotionsMethod->setAccessible(true);
    
    echo "Получаваме всички промоции...<br>";
    $promotions = $getAllPromotionsMethod->invoke($promotionController);
    echo "✅ Получени " . count($promotions) . " промоции<br>";
    
    if (count($promotions) > 0) {
        echo "<h3>Намерени промоции:</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Продукт</th><th>Тип</th><th>Стойност</th><th>Период</th><th>Статус</th></tr>";
        foreach ($promotions as $promotion) {
            echo "<tr>";
            echo "<td>" . $promotion['promotion_id'] . "</td>";
            echo "<td>" . $promotion['product_name'] . "</td>";
            echo "<td>" . ($promotion['discount_type'] == 'percentage' ? 'Процент' : 'Фиксирана') . "</td>";
            echo "<td>" . $promotion['discount_value'] . ($promotion['discount_type'] == 'percentage' ? '%' : ' лв.') . "</td>";
            echo "<td>" . $promotion['start_date'] . " - " . $promotion['end_date'] . "</td>";
            echo "<td>" . ($promotion['status'] ? 'Активна' : 'Неактивна') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Тестваме симулирано създаване на промоция
    echo "<h3>Тест на създаване на промоция</h3>";
    
    // Получаваме един продукт за тест
    $result = $db->query("SELECT product_id, model FROM " . DB_PREFIX . "product WHERE status = 1 LIMIT 1");
    if ($result->num_rows > 0) {
        $product = $result->row;
        echo "Използваме продукт ID: " . $product['product_id'] . " (Модел: " . $product['model'] . ")<br>";
        
        // Симулираме POST данни
        $testData = [
            'promotion_value' => '15',
            'promotion_type' => 'percentage',
            'promotion_date_start' => date('Y-m-d'),
            'promotion_date_end' => date('Y-m-d', strtotime('+30 days')),
            'selected_products' => [$product['product_id']]
        ];
        
        // Тестваме валидацията
        $validateMethod = $reflection->getMethod('validatePromotionData');
        $validateMethod->setAccessible(true);
        
        try {
            $validateMethod->invoke($promotionController, $testData);
            echo "✅ Валидацията премина успешно<br>";
        } catch (Exception $e) {
            echo "❌ Грешка при валидация: " . $e->getMessage() . "<br>";
        }
        
        // Тестваме подготовката на данни
        $prepareMethod = $reflection->getMethod('preparePromotionData');
        $prepareMethod->setAccessible(true);
        
        try {
            $preparedData = $prepareMethod->invoke($promotionController, $testData);
            echo "✅ Данните са подготвени успешно<br>";
            echo "Подготвени данни: <pre>" . print_r($preparedData, true) . "</pre>";
        } catch (Exception $e) {
            echo "❌ Грешка при подготовка на данни: " . $e->getMessage() . "<br>";
        }
        
    } else {
        echo "❌ Няма налични продукти за тест<br>";
    }
    
    echo "<br><h3>✅ Всички тестове на контролера завършиха успешно!</h3>";
    echo "<p><a href='admin/index.php?route=catalog/product/promotion'>Отиди към страницата с промоции</a></p>";
    
} catch (Exception $e) {
    echo "<h3>❌ Грешка при тестване на контролера:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p style='color: red;'>Stack trace: <pre>" . $e->getTraceAsString() . "</pre></p>";
}
?>
