<?php
// Тестов скрипт за синхронизация на промоции
// Дата: 2025-07-08

// Включване на конфигурацията
require_once('admin/config.php');

// Създаване на връзка с базата данни
try {
    $pdo = new PDO("mysql:host=" . DB_HOSTNAME . ";port=" . DB_PORT . ";dbname=" . DB_DATABASE, DB_USERNAME, DB_PASSWORD);
    $pdo->setAttribute(PDO::ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("SET NAMES 'utf8'");
    
    echo "<h2>Тест на синхронизацията на промоции</h2>";
    
    // 1. Създаваме тестова промоция
    echo "<h3>1. Създаване на тестова промоция</h3>";
    
    // Получаваме един продукт за тест
    $stmt = $pdo->query("SELECT product_id, model, price FROM " . DB_PREFIX . "product WHERE status = 1 LIMIT 1");
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        echo "❌ Няма налични продукти за тест<br>";
        exit;
    }
    
    echo "Използваме продукт ID: " . $product['product_id'] . " (Модел: " . $product['model'] . ", Цена: " . number_format($product['price'], 2) . " лв.)<br>";
    
    // Създаваме промоция
    $discountType = 'percentage';
    $discountValue = 20.00;
    $startDate = date('Y-m-d');
    $endDate = date('Y-m-d', strtotime('+30 days'));
    
    $insertSql = "INSERT INTO `" . DB_PREFIX . "promotions` SET
                  `product_id` = :product_id,
                  `discount_type` = :discount_type,
                  `discount_value` = :discount_value,
                  `start_date` = :start_date,
                  `end_date` = :end_date,
                  `status` = 1";
    
    $stmt = $pdo->prepare($insertSql);
    $stmt->bindParam(':product_id', $product['product_id']);
    $stmt->bindParam(':discount_type', $discountType);
    $stmt->bindParam(':discount_value', $discountValue);
    $stmt->bindParam(':start_date', $startDate);
    $stmt->bindParam(':end_date', $endDate);
    
    if ($stmt->execute()) {
        $promotionId = $pdo->lastInsertId();
        echo "✅ Създадена промоция ID: " . $promotionId . " (20% отстъпка)<br>";
        
        // Добавяме и в product_special таблицата
        $originalPrice = $product['price'];
        $specialPrice = $originalPrice * (1 - $discountValue / 100);
        
        $specialSql = "INSERT INTO `" . DB_PREFIX . "product_special` SET
                      `product_id` = :product_id,
                      `customer_group_id` = 1,
                      `priority` = 1,
                      `price` = :price,
                      `date_start` = :date_start,
                      `date_end` = :date_end";
        
        $stmt = $pdo->prepare($specialSql);
        $stmt->bindParam(':product_id', $product['product_id']);
        $stmt->bindParam(':price', $specialPrice);
        $stmt->bindParam(':date_start', $startDate);
        $stmt->bindParam(':date_end', $endDate);
        
        if ($stmt->execute()) {
            echo "✅ Добавен запис в product_special (нова цена: " . number_format($specialPrice, 2) . " лв.)<br>";
        }
        
        // 2. Тестваме деактивиране на промоцията
        echo "<h3>2. Тест на деактивиране на промоция</h3>";
        
        // Деактивираме промоцията
        $updateSql = "UPDATE `" . DB_PREFIX . "promotions` SET `status` = 0 WHERE `promotion_id` = :promotion_id";
        $stmt = $pdo->prepare($updateSql);
        $stmt->bindParam(':promotion_id', $promotionId);
        
        if ($stmt->execute()) {
            echo "✅ Промоцията е деактивирана в promotions таблицата<br>";
            
            // Премахваме от product_special
            $deleteSql = "DELETE FROM `" . DB_PREFIX . "product_special` 
                         WHERE `product_id` = :product_id 
                         AND `date_start` = :date_start 
                         AND `date_end` = :date_end";
            
            $stmt = $pdo->prepare($deleteSql);
            $stmt->bindParam(':product_id', $product['product_id']);
            $stmt->bindParam(':date_start', $startDate);
            $stmt->bindParam(':date_end', $endDate);
            
            if ($stmt->execute()) {
                echo "✅ Записът е премахнат от product_special таблицата<br>";
            }
        }
        
        // 3. Тестваме повторно активиране
        echo "<h3>3. Тест на повторно активиране на промоция</h3>";
        
        // Активираме отново промоцията
        $updateSql = "UPDATE `" . DB_PREFIX . "promotions` SET `status` = 1 WHERE `promotion_id` = :promotion_id";
        $stmt = $pdo->prepare($updateSql);
        $stmt->bindParam(':promotion_id', $promotionId);
        
        if ($stmt->execute()) {
            echo "✅ Промоцията е активирана отново в promotions таблицата<br>";
            
            // Добавяме отново в product_special
            $stmt = $pdo->prepare($specialSql);
            $stmt->bindParam(':product_id', $product['product_id']);
            $stmt->bindParam(':price', $specialPrice);
            $stmt->bindParam(':date_start', $startDate);
            $stmt->bindParam(':date_end', $endDate);
            
            if ($stmt->execute()) {
                echo "✅ Записът е добавен отново в product_special таблицата<br>";
            }
        }
        
        // 4. Проверяваме текущото състояние
        echo "<h3>4. Текущо състояние на таблиците</h3>";
        
        // Проверяваме promotions таблицата
        $stmt = $pdo->prepare("SELECT * FROM " . DB_PREFIX . "promotions WHERE promotion_id = ?");
        $stmt->execute([$promotionId]);
        $promotion = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($promotion) {
            echo "<h4>Промоция в promotions таблицата:</h4>";
            echo "<ul>";
            echo "<li>ID: " . $promotion['promotion_id'] . "</li>";
            echo "<li>Продукт ID: " . $promotion['product_id'] . "</li>";
            echo "<li>Тип: " . $promotion['discount_type'] . "</li>";
            echo "<li>Стойност: " . $promotion['discount_value'] . "%</li>";
            echo "<li>Период: " . $promotion['start_date'] . " - " . $promotion['end_date'] . "</li>";
            echo "<li>Статус: " . ($promotion['status'] ? 'Активна' : 'Неактивна') . "</li>";
            echo "</ul>";
        }
        
        // Проверяваме product_special таблицата
        $stmt = $pdo->prepare("SELECT * FROM " . DB_PREFIX . "product_special WHERE product_id = ? AND date_start = ? AND date_end = ?");
        $stmt->execute([$product['product_id'], $startDate, $endDate]);
        $special = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($special) {
            echo "<h4>Запис в product_special таблицата:</h4>";
            echo "<ul>";
            echo "<li>Продукт ID: " . $special['product_id'] . "</li>";
            echo "<li>Промоционална цена: " . number_format($special['price'], 2) . " лв.</li>";
            echo "<li>Период: " . $special['date_start'] . " - " . $special['date_end'] . "</li>";
            echo "<li>Приоритет: " . $special['priority'] . "</li>";
            echo "</ul>";
        } else {
            echo "<h4>❌ Няма запис в product_special таблицата</h4>";
        }
        
        // 5. Тестваме функционалността чрез контролера
        echo "<h3>5. Тест чрез контролера (симулация)</h3>";
        echo "<p>За да тествате пълната функционалност:</p>";
        echo "<ol>";
        echo "<li><a href='admin/index.php?route=catalog/product/promotion'>Отидете към списъка с промоции</a></li>";
        echo "<li>Използвайте бутона за активиране/деактивиране на промоция ID: " . $promotionId . "</li>";
        echo "<li>Проверете дали записите в product_special се синхронизират автоматично</li>";
        echo "</ol>";
        
        // Опция за изчистване
        echo "<br><h4>Изчистване на тестовите данни:</h4>";
        echo "<p><a href='?cleanup=" . $promotionId . "' style='color: red;'>Изтрий тестовата промоция</a></p>";
        
        // Ако е поискано изчистване
        if (isset($_GET['cleanup']) && $_GET['cleanup'] == $promotionId) {
            echo "<h3>Изчистване на тестовите данни...</h3>";
            
            // Изтриваме от promotions таблицата
            $stmt = $pdo->prepare("DELETE FROM `" . DB_PREFIX . "promotions` WHERE promotion_id = ?");
            $stmt->execute([$promotionId]);
            echo "✅ Изтрита промоция от promotions таблицата<br>";
            
            // Изтриваме от product_special таблицата
            $stmt = $pdo->prepare("DELETE FROM `" . DB_PREFIX . "product_special` WHERE product_id = ? AND date_start = ? AND date_end = ?");
            $stmt->execute([$product['product_id'], $startDate, $endDate]);
            echo "✅ Изтрит запис от product_special таблицата<br>";
            
            echo "<p>Тестовите данни са изчистени. <a href='?'>Обнови страницата</a></p>";
        }
        
    } else {
        echo "❌ Грешка при създаване на тестова промоция<br>";
    }
    
    echo "<br><h3>✅ Тестът на синхронизацията завърши!</h3>";
    
} catch (PDOException $e) {
    echo "<h3>❌ Грешка при работа с базата данни:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<h3>❌ Обща грешка:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>
