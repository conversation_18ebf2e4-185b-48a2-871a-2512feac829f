<?php
// Пълен тест на функционалността за промоции
// Дата: 2025-07-08

// Включване на конфигурацията
require_once('admin/config.php');

// Създаване на връзка с базата данни
try {
    $pdo = new PDO("mysql:host=" . DB_HOSTNAME . ";port=" . DB_PORT . ";dbname=" . DB_DATABASE, DB_USERNAME, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("SET NAMES 'utf8'");
    
    echo "<h2>Пълен тест на функционалността за промоции</h2>";
    
    // 1. Създаваме таблицата
    echo "<h3>1. Създаване на таблицата promotions</h3>";
    $sql = "CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "promotions` (
        `promotion_id` INT(11) NOT NULL AUTO_INCREMENT,
        `product_id` INT(11) NOT NULL,
        `discount_type` ENUM('percentage', 'fixed') NOT NULL,
        `discount_value` DECIMAL(10,2) NOT NULL,
        `start_date` DATE NOT NULL,
        `end_date` DATE NOT NULL,
        `status` TINYINT(1) DEFAULT 1,
        `date_added` DATETIME DEFAULT CURRENT_TIMESTAMP,
        `date_modified` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`promotion_id`),
        KEY `idx_product_id` (`product_id`),
        KEY `idx_status` (`status`),
        KEY `idx_dates` (`start_date`, `end_date`),
        KEY `idx_product_status` (`product_id`, `status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";
    
    $pdo->exec($sql);
    echo "✅ Таблицата е създадена<br>";
    
    // 2. Получаваме тестови продукти
    echo "<h3>2. Получаване на тестови продукти</h3>";
    $stmt = $pdo->query("SELECT p.product_id, p.model, p.price, pd.name 
                        FROM " . DB_PREFIX . "product p 
                        LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id) 
                        WHERE p.status = 1 AND pd.language_id = 1 
                        LIMIT 3");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($products) > 0) {
        echo "✅ Намерени " . count($products) . " продукта за тест<br>";
        foreach ($products as $product) {
            echo "- ID: " . $product['product_id'] . ", Име: " . $product['name'] . ", Модел: " . $product['model'] . ", Цена: " . number_format($product['price'], 2) . " лв.<br>";
        }
    } else {
        echo "❌ Няма налични продукти за тест<br>";
        exit;
    }
    
    // 3. Създаваме тестови промоции
    echo "<h3>3. Създаване на тестови промоции</h3>";
    $testPromotions = [];
    
    foreach ($products as $index => $product) {
        $discountType = ($index % 2 == 0) ? 'percentage' : 'fixed';
        $discountValue = ($discountType == 'percentage') ? (10 + $index * 5) : (5 + $index * 2);
        $startDate = date('Y-m-d');
        $endDate = date('Y-m-d', strtotime('+' . (30 + $index * 10) . ' days'));
        
        $insertSql = "INSERT INTO `" . DB_PREFIX . "promotions` SET
                      `product_id` = :product_id,
                      `discount_type` = :discount_type,
                      `discount_value` = :discount_value,
                      `start_date` = :start_date,
                      `end_date` = :end_date,
                      `status` = 1";
        
        $stmt = $pdo->prepare($insertSql);
        $stmt->bindParam(':product_id', $product['product_id']);
        $stmt->bindParam(':discount_type', $discountType);
        $stmt->bindParam(':discount_value', $discountValue);
        $stmt->bindParam(':start_date', $startDate);
        $stmt->bindParam(':end_date', $endDate);
        
        if ($stmt->execute()) {
            $promotionId = $pdo->lastInsertId();
            $testPromotions[] = $promotionId;
            echo "✅ Създадена промоция ID: " . $promotionId . " за продукт " . $product['name'] . " (" . $discountValue . ($discountType == 'percentage' ? '%' : ' лв.') . ")<br>";
            
            // Създаваме и запис в product_special таблицата
            $originalPrice = $product['price'];
            if ($discountType == 'percentage') {
                $specialPrice = $originalPrice * (1 - $discountValue / 100);
            } else {
                $specialPrice = max(0, $originalPrice - $discountValue);
            }
            
            $specialSql = "INSERT INTO `" . DB_PREFIX . "product_special` SET
                          `product_id` = :product_id,
                          `customer_group_id` = 1,
                          `priority` = 1,
                          `price` = :price,
                          `date_start` = :date_start,
                          `date_end` = :date_end";
            
            $stmt = $pdo->prepare($specialSql);
            $stmt->bindParam(':product_id', $product['product_id']);
            $stmt->bindParam(':price', $specialPrice);
            $stmt->bindParam(':date_start', $startDate);
            $stmt->bindParam(':date_end', $endDate);
            
            if ($stmt->execute()) {
                echo "  ✅ Създаден запис в product_special (нова цена: " . number_format($specialPrice, 2) . " лв.)<br>";
            }
        }
    }
    
    // 4. Проверяваме създадените промоции
    echo "<h3>4. Проверка на създадените промоции</h3>";
    $stmt = $pdo->query("SELECT p.*, pd.name as product_name, pr.model as product_model, pr.price as product_price
                        FROM " . DB_PREFIX . "promotions p 
                        LEFT JOIN " . DB_PREFIX . "product pr ON (p.product_id = pr.product_id)
                        LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                        WHERE pd.language_id = 1
                        ORDER BY p.date_added DESC");
    
    $promotions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($promotions) > 0) {
        echo "✅ Намерени " . count($promotions) . " промоции в базата данни<br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Продукт</th><th>Тип</th><th>Стойност</th><th>Период</th><th>Статус</th><th>Създадена</th></tr>";
        foreach ($promotions as $promotion) {
            echo "<tr>";
            echo "<td>" . $promotion['promotion_id'] . "</td>";
            echo "<td>" . $promotion['product_name'] . " (" . $promotion['product_model'] . ")</td>";
            echo "<td>" . ($promotion['discount_type'] == 'percentage' ? 'Процент' : 'Фиксирана') . "</td>";
            echo "<td>" . $promotion['discount_value'] . ($promotion['discount_type'] == 'percentage' ? '%' : ' лв.') . "</td>";
            echo "<td>" . $promotion['start_date'] . " - " . $promotion['end_date'] . "</td>";
            echo "<td>" . ($promotion['status'] ? 'Активна' : 'Неактивна') . "</td>";
            echo "<td>" . $promotion['date_added'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 5. Тестваме обновяване на промоция
    if (count($testPromotions) > 0) {
        echo "<h3>5. Тест на обновяване на промоция</h3>";
        $promotionToUpdate = $testPromotions[0];
        
        $updateSql = "UPDATE `" . DB_PREFIX . "promotions` SET
                      `discount_value` = 25.00,
                      `date_modified` = NOW()
                      WHERE `promotion_id` = :promotion_id";
        
        $stmt = $pdo->prepare($updateSql);
        $stmt->bindParam(':promotion_id', $promotionToUpdate);
        
        if ($stmt->execute()) {
            echo "✅ Промоция ID: " . $promotionToUpdate . " е обновена успешно<br>";
        }
    }
    
    // 6. Тестваме деактивиране на промоция
    if (count($testPromotions) > 1) {
        echo "<h3>6. Тест на деактивиране на промоция</h3>";
        $promotionToDeactivate = $testPromotions[1];
        
        $deactivateSql = "UPDATE `" . DB_PREFIX . "promotions` SET
                         `status` = 0,
                         `date_modified` = NOW()
                         WHERE `promotion_id` = :promotion_id";
        
        $stmt = $pdo->prepare($deactivateSql);
        $stmt->bindParam(':promotion_id', $promotionToDeactivate);
        
        if ($stmt->execute()) {
            echo "✅ Промоция ID: " . $promotionToDeactivate . " е деактивирана успешно<br>";
        }
    }
    
    echo "<br><h3>✅ Всички тестове завършиха успешно!</h3>";
    echo "<p>Сега можете да отидете към <a href='admin/index.php?route=catalog/product/promotion'>страницата с промоции</a> за да видите резултатите.</p>";
    
    // Опция за изчистване на тестовите данни
    echo "<br><h4>Изчистване на тестовите данни:</h4>";
    echo "<p><a href='?cleanup=1' style='color: red;'>Изтрий всички тестови промоции</a></p>";
    
    // Ако е поискано изчистване
    if (isset($_GET['cleanup']) && $_GET['cleanup'] == '1') {
        echo "<h3>Изчистване на тестовите данни...</h3>";
        
        // Изтриваме от promotions таблицата
        $pdo->exec("DELETE FROM `" . DB_PREFIX . "promotions`");
        echo "✅ Изтрити всички записи от promotions таблицата<br>";
        
        // Изтриваме от product_special таблицата
        $pdo->exec("DELETE FROM `" . DB_PREFIX . "product_special`");
        echo "✅ Изтрити всички записи от product_special таблицата<br>";
        
        echo "<p>Тестовите данни са изчистени. <a href='?'>Обнови страницата</a></p>";
    }
    
} catch (PDOException $e) {
    echo "<h3>❌ Грешка при работа с базата данни:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<h3>❌ Обща грешка:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>
