/**
 * CategoryForm модул за управление на категории
 * Разширява BackendModule с функционалност за категории
 */
(function() {
    'use strict';

    // Проверяваме дали BackendModule е наличен
    if (typeof window.BackendModule !== 'undefined') {
        // Разширяваме BackendModule с CategoryForm функционалност
        Object.assign(window.BackendModule, {
            /**
             * Инициализира autocomplete за родителска категория
             */
            initParentCategoryAutocomplete: function() {
                const parentInput = document.getElementById('input-parent');
                const parentIdInput = document.getElementById('input-parent-id');
                const parentDisplay = document.getElementById('parent-category-display');

                if (!parentInput || !parentIdInput || !parentDisplay) {
                    console.log('[CategoryForm] Parent category elements not found');
                    return;
                }

                console.log('[CategoryForm] Initializing parent category autocomplete');

                let searchTimeout;
                let isSelecting = false;

                // Функция за показване на резултатите
                const showResults = (results) => {
                    let dropdown = document.getElementById('parent-autocomplete-dropdown');
                    if (!dropdown) {
                        dropdown = document.createElement('div');
                        dropdown.id = 'parent-autocomplete-dropdown';
                        dropdown.className = 'absolute z-50 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto';
                        dropdown.style.top = '100%';
                        dropdown.style.left = '0';
                        parentInput.parentNode.style.position = 'relative';
                        parentInput.parentNode.appendChild(dropdown);
                    }

                    dropdown.innerHTML = '';

                    if (results.length === 0) {
                        const noResults = document.createElement('div');
                        noResults.className = 'px-3 py-2 text-gray-500 text-sm';
                        noResults.textContent = 'Няма намерени категории';
                        dropdown.appendChild(noResults);
                    } else {
                        results.forEach(category => {
                            const item = document.createElement('div');
                            item.className = 'px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm border-b border-gray-100 last:border-b-0';
                            item.innerHTML = `
                                <div class="font-medium">${category.name}</div>
                                ${category.path ? `<div class="text-xs text-gray-500">${category.path}</div>` : ''}
                            `;
                            
                            item.addEventListener('click', () => {
                                isSelecting = true;
                                parentInput.value = category.name;
                                parentIdInput.value = category.category_id;
                                this.updateParentCategoryDisplay(category.name, category.path);
                                dropdown.style.display = 'none';
                                setTimeout(() => { isSelecting = false; }, 100);
                            });
                            
                            dropdown.appendChild(item);
                        });
                    }

                    dropdown.style.display = 'block';
                };

                // Функция за скриване на резултатите
                const hideResults = () => {
                    const dropdown = document.getElementById('parent-autocomplete-dropdown');
                    if (dropdown && !isSelecting) {
                        dropdown.style.display = 'none';
                    }
                };

                // Event listener за търсене
                parentInput.addEventListener('input', (e) => {
                    const query = e.target.value.trim();
                    
                    clearTimeout(searchTimeout);
                    
                    if (query.length < 2) {
                        hideResults();
                        if (query.length === 0) {
                            parentIdInput.value = '';
                            this.updateParentCategoryDisplay('', '');
                        }
                        return;
                    }

                    searchTimeout = setTimeout(() => {
                        this.searchParentCategories(query).then(showResults);
                    }, 300);
                });

                // Event listener за focus - показва последните резултати
                parentInput.addEventListener('focus', () => {
                    if (parentInput.value.trim().length >= 2) {
                        this.searchParentCategories(parentInput.value.trim()).then(showResults);
                    }
                });

                // Event listener за blur - скрива резултатите
                parentInput.addEventListener('blur', () => {
                    setTimeout(hideResults, 150);
                });

                // Скриваме dropdown при клик извън него
                document.addEventListener('click', (e) => {
                    if (!parentInput.contains(e.target)) {
                        hideResults();
                    }
                });

                console.log('[CategoryForm] Parent category autocomplete initialized');
            },

            /**
             * Търси родителски категории
             */
            searchParentCategories: function(query) {
                return new Promise((resolve) => {
                    const userToken = this.config.userToken || '';
                    const currentQuery = encodeURIComponent(query);
                    
                    // Получаваме ID на текущата категория и родителската категория за изключване
                    const excludeCategoryId = urlParams.get('category_id') || 0;
                    const excludeParentId = parentIdInput.value || 0;
                    const timestamp = new Date().getTime();

                    // Използваме същия endpoint като в оригиналния метод с добавен exclude_parent_id
                    let fetchUrl = `index.php?route=catalog/category/autocomplete&filter_name=${encodeURIComponent(currentQuery)}&exclude_category_id=${excludeCategoryId}&user_token=${userToken}&_=${timestamp}`;

                    // Добавяме exclude_parent_id ако има избрана родителска категория
                    if (excludeParentId && excludeParentId != '0') {
                        fetchUrl += `&exclude_parent_id=${excludeParentId}`;
                    }

                    console.log('[CategoryForm] Searching categories with URL:', fetchUrl);

                    fetch(fetchUrl)
                        .then(response => response.json())
                        .then(data => {
                            console.log('[CategoryForm] Search results:', data);
                            resolve(data || []);
                        })
                        .catch(error => {
                            console.error('[CategoryForm] Search error:', error);
                            resolve([]);
                        });
                });
            },

            /**
             * Актуализира показването на избраната родителска категория
             */
            updateParentCategoryDisplay: function(name, path) {
                const parentDisplay = document.getElementById('parent-category-display');
                if (!parentDisplay) return;

                if (name) {
                    parentDisplay.innerHTML = `
                        <div class="flex items-center justify-between bg-blue-50 border border-blue-200 rounded px-3 py-2">
                            <div>
                                <div class="font-medium text-blue-800">${name}</div>
                                ${path ? `<div class="text-xs text-blue-600">${path}</div>` : ''}
                            </div>
                            <button type="button" class="text-blue-600 hover:text-blue-800 ml-2" onclick="BackendModule.clearParentCategory()">
                                <i class="ri-close-line"></i>
                            </button>
                        </div>
                    `;
                } else {
                    parentDisplay.innerHTML = '';
                }
            },

            /**
             * Изчиства избраната родителска категория
             */
            clearParentCategory: function() {
                const parentInput = document.getElementById('input-parent');
                const parentIdInput = document.getElementById('input-parent-id');
                
                if (parentInput) parentInput.value = '';
                if (parentIdInput) parentIdInput.value = '';
                
                this.updateParentCategoryDisplay('', '');
                
                console.log('[CategoryForm] Parent category cleared');
            },

            /**
             * Инициализира SEO URL генериране
             */
            initSeoUrlGeneration: function() {
                const nameInputs = document.querySelectorAll('input[name^="category_description"][name$="[name]"]');
                const generateButton = document.getElementById('generate-seo-url');
                
                if (!generateButton) {
                    console.log('[CategoryForm] SEO URL generate button not found');
                    return;
                }

                console.log('[CategoryForm] Initializing SEO URL generation');

                generateButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    // Намираме активния език
                    const activeTab = document.querySelector('.nav-link.active[data-bs-toggle="tab"]');
                    if (!activeTab) return;
                    
                    const languageId = activeTab.getAttribute('href').replace('#language', '');
                    const nameInput = document.querySelector(`input[name="category_description[${languageId}][name]"]`);
                    const seoUrlInput = document.querySelector(`input[name="category_seo_url[${languageId}][keyword]"]`);
                    
                    if (!nameInput || !seoUrlInput) return;
                    
                    const categoryName = nameInput.value.trim();
                    if (!categoryName) {
                        alert('Моля, въведете име на категорията първо');
                        return;
                    }
                    
                    // Генерираме SEO URL
                    const seoUrl = this.generateSeoUrl(categoryName);
                    seoUrlInput.value = seoUrl;
                    
                    console.log('[CategoryForm] Generated SEO URL:', seoUrl);
                });
            },

            /**
             * Генерира SEO URL от текст
             */
            generateSeoUrl: function(text) {
                // Транслитерация от кирилица към латиница
                const transliterationMap = {
                    'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ж': 'zh', 'з': 'z',
                    'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm', 'н': 'n', 'о': 'o', 'п': 'p',
                    'р': 'r', 'с': 's', 'т': 't', 'у': 'u', 'ф': 'f', 'х': 'h', 'ц': 'ts', 'ч': 'ch',
                    'ш': 'sh', 'щ': 'sht', 'ъ': 'a', 'ь': 'y', 'ю': 'yu', 'я': 'ya',
                    'А': 'A', 'Б': 'B', 'В': 'V', 'Г': 'G', 'Д': 'D', 'Е': 'E', 'Ж': 'Zh', 'З': 'Z',
                    'И': 'I', 'Й': 'Y', 'К': 'K', 'Л': 'L', 'М': 'M', 'Н': 'N', 'О': 'O', 'П': 'P',
                    'Р': 'R', 'С': 'S', 'Т': 'T', 'У': 'U', 'Ф': 'F', 'Х': 'H', 'Ц': 'Ts', 'Ч': 'Ch',
                    'Ш': 'Sh', 'Щ': 'Sht', 'Ъ': 'A', 'Ь': 'Y', 'Ю': 'Yu', 'Я': 'Ya'
                };

                let result = text;
                
                // Транслитерация
                for (const [cyrillic, latin] of Object.entries(transliterationMap)) {
                    result = result.replace(new RegExp(cyrillic, 'g'), latin);
                }
                
                // Почистване и форматиране
                result = result
                    .toLowerCase()
                    .replace(/[^a-z0-9\s-]/g, '') // Премахваме всички символи освен букви, цифри, интервали и тирета
                    .replace(/\s+/g, '-') // Заменяме интервалите с тирета
                    .replace(/-+/g, '-') // Заменяме множествените тирета с едно
                    .replace(/^-|-$/g, ''); // Премахваме тиретата в началото и края
                
                return result;
            },

            /**
             * Инициализира image picker за категория
             */
            initCategoryImagePicker: function() {
                const imageInput = document.getElementById('input-image');
                const imagePreview = document.getElementById('image-preview');
                const imagePickerBtn = document.getElementById('image-picker-btn');
                const clearImageBtn = document.getElementById('clear-image-btn');

                if (!imageInput || !imagePreview || !imagePickerBtn) {
                    console.log('[CategoryForm] Image picker elements not found');
                    return;
                }

                console.log('[CategoryForm] Initializing category image picker');

                // Event listener за image picker бутона
                imagePickerBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    // Отваряме image manager модала
                    if (typeof window.ProductImages !== 'undefined' && window.ProductImages.openImageManager) {
                        const currentDirectory = this.getCategoryImageDirectory();
                        
                        window.ProductImages.openImageManager({
                            singleSelection: true,
                            currentDirectory: currentDirectory,
                            onSelect: (selectedImages) => {
                                if (selectedImages && selectedImages.length > 0) {
                                    const selectedImage = selectedImages[0];
                                    imageInput.value = selectedImage.path;
                                    this.updateImagePreview(selectedImage.path);
                                    console.log('[CategoryForm] Image selected:', selectedImage.path);
                                }
                            }
                        });
                    } else {
                        console.error('[CategoryForm] ProductImages module not available');
                    }
                });

                // Event listener за clear бутона
                if (clearImageBtn) {
                    clearImageBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        imageInput.value = '';
                        this.updateImagePreview('');
                        console.log('[CategoryForm] Image cleared');
                    });
                }

                // Инициализираме preview ако има стойност
                if (imageInput.value) {
                    this.updateImagePreview(imageInput.value);
                }
            },

            /**
             * Актуализира preview на изображението
             */
            updateImagePreview: function(imagePath) {
                const imagePreview = document.getElementById('image-preview');
                const clearImageBtn = document.getElementById('clear-image-btn');
                
                if (!imagePreview) return;

                if (imagePath) {
                    // Показваме изображението
                    const imageUrl = `../image/${imagePath}`;
                    imagePreview.innerHTML = `
                        <div class="relative inline-block">
                            <img src="${imageUrl}" alt="Category Image" class="w-32 h-32 object-cover rounded border">
                        </div>`;
                    
                    // Показваме clear бутона
                    if (clearImageBtn) {
                        clearImageBtn.style.display = 'inline-flex';
                    }
                } else {
                    // Показваме placeholder
                    imagePreview.innerHTML = `
                        <div class="w-32 h-32 bg-gray-100 rounded border flex items-center justify-center">
                            <div class="text-center text-gray-500">
                                <div class="w-5 h-5 flex items-center justify-center"><i class="ri-folder-image-line"></i></div>
                            </div>
                        </div>`;
                    
                    // Скриваме clear бутона
                    if (clearImageBtn) {
                        clearImageBtn.style.display = 'none';
                    }
                }
            },

            /**
             * Генерира template за image picker
             */
            generateImagePickerTemplate: function() {
                return `
                        <div class="flex items-center space-x-3">
                            <div id="image-preview" class="flex-shrink-0">
                                <div class="w-32 h-32 bg-gray-100 rounded border flex items-center justify-center">
                                    <div class="text-center text-gray-500">
                                        <div class="w-5 h-5 flex items-center justify-center"><i class="ri-folder-image-line"></i></div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col space-y-2">
                                <button type="button" id="image-picker-btn" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="ri-folder-image-line mr-2"></i>
                                    Избери изображение
                                </button>
                                <button type="button" id="clear-image-btn" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" style="display: none;">
                                    <i class="ri-close-line mr-2"></i>
                                    Премахни
                                </button>
                            </div>
                        </div>`;
                
                if (this.config.imagePickerTemplate) {
                    return this.config.imagePickerTemplate;
                } else {
                    return `
                        <div class="flex items-center space-x-3">
                            <div id="image-preview" class="flex-shrink-0">
                                <div class="w-32 h-32 bg-gray-100 rounded border flex items-center justify-center">
                                    <div class="text-center text-gray-500">
                                        <div class="w-5 h-5 flex items-center justify-center"><i class="ri-folder-image-line"></i></div>
                                    </div>
                                </div>
                            </div>
                        </div>`;
                }
            },

            /**
             * Връща директорията на текущото изображение на категорията
             */
            getCategoryImageDirectory: function() {
                const imageInput = document.getElementById('input-image');
                if (!imageInput || !imageInput.value) {
                    return '';
                }

                const pathParts = imageInput.value.split('/');
                if (pathParts.length > 1) {
                    pathParts.pop(); // remove filename
                    if (pathParts[0] === 'catalog') {
                        pathParts.shift(); // remove 'catalog'
                    }
                    return pathParts.join('/');
                }
                return '';
            },
        });

        console.log('[CategoryForm] BackendModule extended successfully');
    } else {
        console.error('[CategoryForm] BackendModule not available for extension');
    }

    console.log('[CategoryForm] Module loaded');
})();
