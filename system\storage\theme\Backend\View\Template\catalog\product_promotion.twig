<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto overflow-x-hidden bg-gray-50" style="padding-bottom: 200px">

    <!-- Product Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex flex-col md:flex-row md:items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Продуктови промоции</h1>
                <p class="text-gray-500 mt-1">Управление на промоциите на продукти в магазина</p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-col sm:flex-row gap-2">
                <a href="index.php?route=catalog/product/promotion/add&user_token={{ user_token }}" class="px-4 py-2 bg-orange-500 text-white rounded-button hover:bg-orange-600 transition-colors whitespace-nowrap flex items-center !rounded-button">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-percent-line"></i>
                    </div>
                    <span>Задай промоция</span>
                </a>
                <button onclick="window.location.reload()" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-add-line"></i>
                    </div>
                    <span>Обнови страницата</span>
                </a>
            </div>
        </div>
    </div>




    <!-- Promotions List -->
    <div class="bg-white border border-gray-200 rounded-lg mx-6 mb-6">
        <!-- Header -->
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col md:flex-row md:items-center justify-between">
                <div>
                    <h2 class="text-lg font-semibold text-gray-800">Активни промоции</h2>
                    <p class="text-gray-500 text-sm mt-1">Списък с всички създадени промоции</p>
                </div>
                <div class="mt-4 md:mt-0 flex flex-col sm:flex-row gap-2">
                    <!-- Филтри -->
                    <div class="flex gap-2">
                        <select id="status-filter" class="px-3 py-2 border border-gray-300 rounded-button text-sm bg-white">
                            <option value="">Всички статуси</option>
                            <option value="1">Активни</option>
                            <option value="0">Неактивни</option>
                        </select>
                        <input type="text" id="product-filter" placeholder="Търси продукт..." class="px-3 py-2 border border-gray-300 rounded-button text-sm">
                    </div>
                </div>
            </div>
        </div>

        <!-- Table -->
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Наименование на промоцията</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Отстъпка</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Период</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Статус</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Създадена</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="promotions-table-body">
                    {% if promotions %}
                        {% for promotion in promotions %}
                        <tr class="hover:bg-gray-50" data-promotion-id="{{ promotion.promotion_id }}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ promotion.promotion_name ?: promotion.promotion_description }}</div>
                                        {# {% if promotion.promotion_name and promotion.promotion_description != promotion.promotion_name %}
                                            <div class="text-xs text-gray-400">{{ promotion.promotion_description }}</div>
                                        {% endif %}
                                        {% if promotion.category_name %}
                                            <div class="text-sm text-gray-500">
                                                <i class="ri-folder-line"></i> Категория: {{ promotion.category_name }}
                                            </div>
                                        {% endif %} #}
                                        <div class="text-sm text-gray-500">
                                            <i class="ri-shopping-cart-line"></i> {{ ' ' }}{{ promotion.products_count }} продукта
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {% if promotion.discount_type == 'percentage' %}
                                        {{ promotion.discount_value }}%
                                    {% else %}
                                        {{ promotion.discount_value|number_format(2) }} лв.
                                    {% endif %}
                                </div>
                                <div class="text-sm text-gray-500">
                                    {% if promotion.discount_type == 'percentage' %}
                                        Процентна отстъпка
                                    {% else %}
                                        Фиксирана отстъпка
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ promotion.start_date|date('d.m.Y') }} - {{ promotion.end_date|date('d.m.Y') }}</div>
                                <div class="text-sm text-gray-500">
                                    {% set today = "now"|date('Y-m-d') %}
                                    {% if promotion.start_date > today %}
                                        Предстояща
                                    {% elseif promotion.end_date < today %}
                                        Изтекла
                                    {% else %}
                                        Активна
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ promotion.status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ promotion.status ? 'Активна' : 'Неактивна' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ promotion.date_added|date('d.m.Y H:i') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <a href="index.php?route=catalog/product/promotion/edit&promotion_id={{ promotion.promotion_id }}&user_token={{ user_token }}" class="text-indigo-600 hover:text-indigo-900 p-1" title="Редактирай">
                                        <i class="ri-edit-line"></i>
                                    </a>
                                    <button onclick="BackendModule.togglePromotionStatus({{ promotion.promotion_id }})" class="text-{{ promotion.status ? 'orange' : 'green' }}-600 hover:text-{{ promotion.status ? 'orange' : 'green' }}-900 p-1" title="{{ promotion.status ? 'Деактивирай' : 'Активирай' }}">
                                        <i class="ri-{{ promotion.status ? 'pause' : 'play' }}-circle-line"></i>
                                    </button>
                                    <button onclick="BackendModule.deletePromotion({{ promotion.promotion_id }})" class="text-red-600 hover:text-red-900 p-1" title="Изтрий">
                                        <i class="ri-delete-bin-line"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="ri-percent-line text-4xl mb-4"></i>
                                    <p class="text-lg font-medium">Няма създадени промоции</p>
                                    <p class="text-sm">Започнете като създадете първата си промоция</p>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-promotion-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
        <div class="bg-white rounded-lg w-[90%] lg:w-[30%] max-w-none mx-4">
            <!-- Header -->
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Потвърждение за изтриване</h3>
                <button id="close-delete-promotion" class="text-gray-400 hover:text-gray-500">
                    <div class="w-6 h-6 flex items-center justify-center">
                        <i class="ri-close-line"></i>
                    </div>
                </button>
            </div>

            <!-- Content -->
            <div class="p-6">
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <i class="ri-error-warning-line text-red-500 text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-900">Сигурни ли сте, че искате да изтриете тази промоция?</p>
                        <p class="text-sm text-gray-500 mt-1">Това действие не може да бъде отменено.</p>
                    </div>
                </div>
                <input type="hidden" id="delete-promotion-id">
            </div>

            <!-- Footer -->
            <div class="p-6 border-t border-gray-200">
                <div class="flex justify-end space-x-2">
                    <button type="button" id="cancel-delete-promotion" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 text-sm">Отказ</button>
                    <button type="button" id="confirm-delete-promotion" class="px-4 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 text-sm">Изтрий</button>
                </div>
            </div>
        </div>
    </div>

</main>


