<?php

namespace Theme25\Backend\Controller\Catalog;

class Product extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'catalog/product');
    }

    // method index() - subcontroller Index

    // method edit() - subcontroller Edit


    // method save() - subcontroller Save
    

	/**
     * Обработва AJAX заявките за автодопълване
     */
    public function autocomplete() {
        $json = [];

        ob_start();

        if ($this->requestGet('type') && $this->requestGet('type')) {
            $type = $this->requestGet('type');
            
            // Зареждане на съответния субконтролер
            $sub_controller = $this->setBackendSubController('Catalog/Product/' . ucfirst($type) . 'Autocomplete', $this);
            
            if ($sub_controller && is_callable([$sub_controller, 'autocomplete'])) {
                $json = $sub_controller->autocomplete($this->requestGet());
            } else {
                $json['error'] = 'Методът не е намерен';
            }
        } else {
            $json['error'] = 'Липсващ параметър type';
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }
        
        $this->setJSONResponseOutput($json);

    }

    // /**
    //  * Обработва AJAX заявките за промоции
    //  */
    // public function promotion() {
    //     $json = [];

    //     ob_start();

    //     // Зареждане на суб-контролера за промоции
    //     $sub_controller = $this->setBackendSubController('Catalog/Product/Promotion', $this);

    //     if ($sub_controller && is_callable([$sub_controller, 'save'])) {
    //         $json = $sub_controller->save($this->requestPost());
    //     } else {
    //         $json['error'] = 'Методът за промоции не е намерен';
    //     }

    //     $output = ob_get_clean();
    //     if($output) {
    //         $json['error'] = $output;
    //     }

    //     $this->setJSONResponseOutput($json);
    // }


}
