<?php
// Тестов скрипт за създаване на таблицата promotions
// Дата: 2025-07-08

// Включване на конфигурацията
require_once('admin/config.php');

// Създаване на връзка с базата данни
try {
    $pdo = new PDO("mysql:host=" . DB_HOSTNAME . ";port=" . DB_PORT . ";dbname=" . DB_DATABASE, DB_USERNAME, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("SET NAMES 'utf8'");
    
    echo "<h2>Създаване на таблицата promotions</h2>";
    
    // SQL за създаване на таблицата
    $sql = "CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "promotions` (
        `promotion_id` INT(11) NOT NULL AUTO_INCREMENT,
        `product_id` INT(11) NOT NULL,
        `discount_type` ENUM('percentage', 'fixed') NOT NULL,
        `discount_value` DECIMAL(10,2) NOT NULL,
        `start_date` DATE NOT NULL,
        `end_date` DATE NOT NULL,
        `status` TINYINT(1) DEFAULT 1,
        `date_added` DATETIME DEFAULT CURRENT_TIMESTAMP,
        `date_modified` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`promotion_id`),
        KEY `idx_product_id` (`product_id`),
        KEY `idx_status` (`status`),
        KEY `idx_dates` (`start_date`, `end_date`),
        KEY `idx_product_status` (`product_id`, `status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";
    
    // Изпълняваме SQL заявката
    $pdo->exec($sql);
    echo "✅ Таблицата " . DB_PREFIX . "promotions е създадена успешно!<br><br>";
    
    // Проверяваме структурата на таблицата
    echo "<h3>Структура на таблицата:</h3>";
    $stmt = $pdo->query("DESCRIBE " . DB_PREFIX . "promotions");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Поле</th><th>Тип</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Проверяваме индексите
    echo "<h3>Индекси на таблицата:</h3>";
    $stmt = $pdo->query("SHOW INDEX FROM " . DB_PREFIX . "promotions");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Таблица</th><th>Уникален</th><th>Име на ключ</th><th>Колона</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['Table'] . "</td>";
        echo "<td>" . ($row['Non_unique'] ? 'Не' : 'Да') . "</td>";
        echo "<td>" . $row['Key_name'] . "</td>";
        echo "<td>" . $row['Column_name'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Тестваме вмъкване на тестови данни
    echo "<h3>Тест на вмъкване на данни:</h3>";
    
    // Първо получаваме един продукт за тест
    $stmt = $pdo->query("SELECT product_id, model FROM " . DB_PREFIX . "product WHERE status = 1 LIMIT 1");
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($product) {
        echo "Използваме продукт ID: " . $product['product_id'] . " (Модел: " . $product['model'] . ")<br>";
        
        // Вмъкваме тестова промоция
        $insertSql = "INSERT INTO `" . DB_PREFIX . "promotions` SET
                      `product_id` = :product_id,
                      `discount_type` = 'percentage',
                      `discount_value` = 10.00,
                      `start_date` = CURDATE(),
                      `end_date` = DATE_ADD(CURDATE(), INTERVAL 30 DAY),
                      `status` = 1";
        
        $stmt = $pdo->prepare($insertSql);
        $stmt->bindParam(':product_id', $product['product_id']);
        
        if ($stmt->execute()) {
            $promotionId = $pdo->lastInsertId();
            echo "✅ Тестова промоция е създадена с ID: " . $promotionId . "<br>";
            
            // Проверяваме дали записът е вмъкнат правилно
            $stmt = $pdo->prepare("SELECT * FROM " . DB_PREFIX . "promotions WHERE promotion_id = ?");
            $stmt->execute([$promotionId]);
            $promotion = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($promotion) {
                echo "<h4>Данни на тестовата промоция:</h4>";
                echo "<ul>";
                echo "<li>ID: " . $promotion['promotion_id'] . "</li>";
                echo "<li>Продукт ID: " . $promotion['product_id'] . "</li>";
                echo "<li>Тип отстъпка: " . $promotion['discount_type'] . "</li>";
                echo "<li>Стойност: " . $promotion['discount_value'] . "%</li>";
                echo "<li>Начална дата: " . $promotion['start_date'] . "</li>";
                echo "<li>Крайна дата: " . $promotion['end_date'] . "</li>";
                echo "<li>Статус: " . ($promotion['status'] ? 'Активна' : 'Неактивна') . "</li>";
                echo "<li>Създадена: " . $promotion['date_added'] . "</li>";
                echo "</ul>";
                
                // Изтриваме тестовата промоция
                $stmt = $pdo->prepare("DELETE FROM " . DB_PREFIX . "promotions WHERE promotion_id = ?");
                if ($stmt->execute([$promotionId])) {
                    echo "✅ Тестовата промоция е изтрита успешно<br>";
                }
            }
        } else {
            echo "❌ Грешка при вмъкване на тестова промоция<br>";
        }
    } else {
        echo "❌ Няма налични продукти за тест<br>";
    }
    
    echo "<br><h3>✅ Всички тестове завършиха успешно!</h3>";
    echo "<p><a href='admin/index.php?route=catalog/product/promotion'>Отиди към страницата с промоции</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>❌ Грешка при работа с базата данни:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<h3>❌ Обща грешка:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>
