<?php

namespace Theme25;

class Data {
    private static $instance = null;
    private $envLoader;

    private function __construct() {
        $this->envLoader = new EnvLoader();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Получава стойност от .env файла
     *
     * @param string $key Ключ в .env файла
     * @param string|null $constantName Име на константа за fallback
     * @param mixed $default Стойност по подразбиране
     * @return mixed
     */
    public function get($key, $constantName = null, $default = null) {
        // Първо проверяваме .env файла
        $value = $this->envLoader->get($key);
        
        if ($value !== null) {
            return $value;
        }
        
        // Ако няма стойност в .env, проверяваме константата
        if ($constantName && defined($constantName)) {
            return constant($constantName);
        }
        
        // Връщаме стойността по подразбиране
        return $default;
    }

    /**
     * Проверява дали ключ съществува в .env файла
     *
     * @param string $key Ключ в .env файла
     * @return bool
     */
    public function has($key) {
        return $this->envLoader->has($key);
    }

    /**
     * Връща всички настройки от .env файла
     *
     * @return array
     */
    public function all() {
        return $this->envLoader->all();
    }

    /**
     * Връща пътя към сървъра с изображения
     * Ако IMAGE_SERVER_PATH е зададен в .env файла, връща тази стойност
     * В противен случай връща стойността на константата DIR_IMAGE
     *
     * @return string
     */
    public function getImageServerPath($theme_path = true) {
        if(!$theme_path) return DIR_IMAGE;
        return $this->get('IMAGE_SERVER_PATH', 'DIR_IMAGE', '');
    }

    public function getImageCatalogPath($theme_path = true) {
        return $theme_path ? $this->getImageServerPath() . 'catalog/' : DIR_IMAGE . 'catalog/';
    }

    public function getImageWebUrl($theme_path = true) {
        $standardUrl = HTTPS_CATALOG . 'image/';
        if(!$theme_path) return $standardUrl;
        return $this->get('IMAGE_WEB_URL', null, $standardUrl);
    }

    /**
     * Връща URL към сървъра с изображения
     * Алиас на getImageWebUrl за съвместимост
     *
     * @param bool $theme_path Дали да използва theme пътя
     * @return string
     */
    public function getImageServerUrl($theme_path = true) {
        return $this->getImageWebUrl($theme_path);
    }

    public function getNoImageFile($theme_path = true) {
        $file = $this->get('NO_IMAGE_FILE', null, 'no_image.png');
        $imagePath = $this->getImageServerPath($theme_path);
        if(file_exists($imagePath . $file)) {
            return $file;
        }
        return false;
    }

    /**
     * Връща дали е разрешена втората база данни
     *
     * @return bool
     */
    public function isSecondDbEnabled() {
        return (bool)$this->get('SECOND_DB_ENABLED', null, false);
    }

    /**
     * Връща идентификатора на втората база данни
     *
     * @return string
     */
    public function getSecondDbIdentifier() {
        return $this->get('SECOND_DB_IDENTIFIER', null, 'second_db');
    }

    /**
     * Връща префикса на таблиците за втората база данни
     *
     * @return string
     */
    public function getSecondDbPrefix() {
        return $this->get('SECOND_DB_PREFIX', null, 'oc_');
    }

    /**
     * Връща настройките за втората база данни
     *
     * @return array
     */
    public function getSecondDbConfig() {
        return [
            'hostname' => $this->get('SECOND_DB_HOSTNAME', null, 'localhost'),
            'username' => $this->get('SECOND_DB_USERNAME', null, ''),
            'password' => $this->get('SECOND_DB_PASSWORD', null, ''),
            'database' => $this->get('SECOND_DB_DATABASE', null, ''),
            'port' => $this->get('SECOND_DB_PORT', null, '3306'),
            'prefix' => $this->getSecondDbPrefix()
        ];
    }

    /**
     * Връща настройките за Redis
     *
     * @return array
     */
    public function getRedisConfig() {
        return [
            'enabled' => (bool)$this->get('REDIS_ENABLED', null, false),
            'host' => $this->get('REDIS_HOST', null, '127.0.0.1'),
            'port' => (int)$this->get('REDIS_PORT', null, 6379),
            'password' => $this->get('REDIS_PASSWORD', null, ''),
            'database' => (int)$this->get('REDIS_DATABASE', null, 0),
            'prefix' => $this->get('REDIS_PREFIX', null, 'oc_'),
            'timeout' => (float)$this->get('REDIS_TIMEOUT', null, 2.5)
        ];
    }

    /**
     * Връща настройките за кеширане
     *
     * @return array
     */
    public function getCacheConfig() {
        return [
            'driver' => $this->get('CACHE_DRIVER', null, 'file'),
            'ttl' => (int)$this->get('CACHE_TTL', null, 3600),
            'prefix' => $this->get('CACHE_PREFIX', null, 'oc_cache_')
        ];
    }

    /**
     * Връща настройките за логове
     *
     * @return array
     */
    public function getLogConfig() {
        return [
            'level' => $this->get('LOG_LEVEL', null, 'error'),
            'max_files' => (int)$this->get('LOG_MAX_FILES', null, 30),
            'max_size' => $this->get('LOG_MAX_SIZE', null, '10MB'),
            'developer_mode' => (bool)$this->get('DEVELOPER_MODE', null, false)
        ];
    }

    /**
     * Връща настройките за сесии
     *
     * @return array
     */
    public function getSessionConfig() {
        return [
            'driver' => $this->get('SESSION_DRIVER', null, 'file'),
            'lifetime' => (int)$this->get('SESSION_LIFETIME', null, 3600),
            'path' => $this->get('SESSION_PATH', null, DIR_SESSION),
            'cookie_secure' => (bool)$this->get('SESSION_COOKIE_SECURE', null, false),
            'cookie_httponly' => (bool)$this->get('SESSION_COOKIE_HTTPONLY', null, true)
        ];
    }

    /**
     * Връща настройките за email
     *
     * @return array
     */
    public function getEmailConfig() {
        return [
            'driver' => $this->get('MAIL_DRIVER', null, 'mail'),
            'host' => $this->get('MAIL_HOST', null, ''),
            'port' => (int)$this->get('MAIL_PORT', null, 587),
            'username' => $this->get('MAIL_USERNAME', null, ''),
            'password' => $this->get('MAIL_PASSWORD', null, ''),
            'encryption' => $this->get('MAIL_ENCRYPTION', null, 'tls'),
            'from_address' => $this->get('MAIL_FROM_ADDRESS', null, ''),
            'from_name' => $this->get('MAIL_FROM_NAME', null, '')
        ];
    }

    /**
     * Връща настройките за API
     *
     * @return array
     */
    public function getApiConfig() {
        return [
            'enabled' => (bool)$this->get('API_ENABLED', null, false),
            'rate_limit' => (int)$this->get('API_RATE_LIMIT', null, 100),
            'rate_limit_window' => (int)$this->get('API_RATE_LIMIT_WINDOW', null, 3600),
            'auth_method' => $this->get('API_AUTH_METHOD', null, 'token'),
            'cors_enabled' => (bool)$this->get('API_CORS_ENABLED', null, false),
            'cors_origins' => $this->get('API_CORS_ORIGINS', null, '*')
        ];
    }

    /**
     * Връща настройките за debug режим
     *
     * @return array
     */
    public function getDebugConfig() {
        return [
            'enabled' => (bool)$this->get('DEBUG_ENABLED', null, false),
            'show_errors' => (bool)$this->get('DEBUG_SHOW_ERRORS', null, false),
            'log_queries' => (bool)$this->get('DEBUG_LOG_QUERIES', null, false),
            'profiler' => (bool)$this->get('DEBUG_PROFILER', null, false)
        ];
    }

    /**
     * Връща настройките за сигурност
     *
     * @return array
     */
    public function getSecurityConfig() {
        return [
            'csrf_protection' => (bool)$this->get('SECURITY_CSRF_PROTECTION', null, true),
            'xss_protection' => (bool)$this->get('SECURITY_XSS_PROTECTION', null, true),
            'sql_injection_protection' => (bool)$this->get('SECURITY_SQL_INJECTION_PROTECTION', null, true),
            'rate_limiting' => (bool)$this->get('SECURITY_RATE_LIMITING', null, false),
            'ip_whitelist' => $this->get('SECURITY_IP_WHITELIST', null, ''),
            'ip_blacklist' => $this->get('SECURITY_IP_BLACKLIST', null, '')
        ];
    }
}
