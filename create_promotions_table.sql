-- SQL скрипт за създаване на таблица за промоции
-- Дата: 2025-07-08
-- Описание: Създава таблица за управление на промоции на продукти

CREATE TABLE IF NOT EXISTS `oc_promotions` (
    `promotion_id` INT(11) NOT NULL AUTO_INCREMENT,
    `product_id` INT(11) NOT NULL,
    `discount_type` ENUM('percentage', 'fixed') NOT NULL,
    `discount_value` DECIMAL(10,2) NOT NULL,
    `start_date` DATE NOT NULL,
    `end_date` DATE NOT NULL,
    `status` TINYINT(1) DEFAULT 1,
    `date_added` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `date_modified` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`promotion_id`),
    <PERSON>EY `idx_product_id` (`product_id`),
    KEY `idx_status` (`status`),
    <PERSON>EY `idx_dates` (`start_date`, `end_date`),
    KEY `idx_product_status` (`product_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Добавяне на коментари към колоните
ALTER TABLE `oc_promotions` 
    MODIFY COLUMN `promotion_id` INT(11) NOT NULL AUTO_INCREMENT COMMENT 'Уникален идентификатор на промоцията',
    MODIFY COLUMN `product_id` INT(11) NOT NULL COMMENT 'ID на продукта',
    MODIFY COLUMN `discount_type` ENUM('percentage', 'fixed') NOT NULL COMMENT 'Тип отстъпка - процент или фиксирана сума',
    MODIFY COLUMN `discount_value` DECIMAL(10,2) NOT NULL COMMENT 'Стойност на отстъпката',
    MODIFY COLUMN `start_date` DATE NOT NULL COMMENT 'Начална дата на промоцията',
    MODIFY COLUMN `end_date` DATE NOT NULL COMMENT 'Крайна дата на промоцията',
    MODIFY COLUMN `status` TINYINT(1) DEFAULT 1 COMMENT 'Статус - 1 активна, 0 неактивна',
    MODIFY COLUMN `date_added` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Дата на създаване',
    MODIFY COLUMN `date_modified` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Дата на последна промяна';
