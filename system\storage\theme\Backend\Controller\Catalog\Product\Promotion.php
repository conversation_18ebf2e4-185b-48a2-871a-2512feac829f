<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Promotion extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
    }

    public function execute() {
        $this->setTitle('Промоции');
        $this->initAdminData();
        $this->addBackendScriptWithVersion('product-promotion.js', 'footer');

        // Създаваме таблицата ако не съществува
        $this->createPromotionsTable();

        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('catalog/product_promotion');
    }

    public function prepareData() {
        // Зареждаме всички промоции за показване в листинга
        $promotions = $this->getAllPromotions();

        $this->setData([
            'back_url' => $this->getAdminLink('catalog/product/promotion'),
            'promotions' => $promotions,
            'add_new_url' => $this->getAdminLink('catalog/product/promotion/add')
        ]);
    }

    /**
     * Обработва AJAX заявките за промоции
     */
    public function submit() {
        $json = [];

        ob_start();
    
        $json = $this->save($this->requestPost());
    
        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }
    
    /**
     * Обработва AJAX заявката за запазване на промоция
     * 
     * @param array $post POST данни от заявката
     * @return array
     */
    public function save($post) {
        $json = [];

        try {
            // Проверка за валидна AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }
            
            // Валидация на входните данни
            $this->validatePromotionData($post);

            
            // Подготвяне на данните за промоцията
            $promotionData = $this->preparePromotionData($post);
            
            // Запазване на промоцията
            $this->savePromotion($promotionData);
            
            $json['success'] = 'Промоцията беше успешно приложена!';
            
        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }
        
        return $json;
    }
    
    /**
     * Валидира данните за промоцията
     *
     * @param array $post
     * @throws \Exception
     */
    private function validatePromotionData($post) {
        // Проверка на стойността
        if (empty($post['promotion_value'])) {
            throw new \Exception('Моля въведете стойност');
        }

        $value = (float)$post['promotion_value'];
        if ($value <= 0) {
            throw new \Exception('Стойността трябва да бъде по-голяма от 0');
        }

        // Проверка на типа
        if (empty($post['promotion_type']) || !in_array($post['promotion_type'], ['percentage', 'fixed'])) {
            throw new \Exception('Невалиден тип промоция');
        }

        // Специфична валидация за процент
        if ($post['promotion_type'] === 'percentage' && $value > 100) {
            throw new \Exception('Процентът не може да бъде по-голям от 100');
        }
        
        // Проверка на датите - поне една дата трябва да бъде въведена
        $hasStartDate = !empty($post['promotion_date_start']);
        $hasEndDate = !empty($post['promotion_date_end']);

        if (!$hasStartDate && !$hasEndDate) {
            throw new \Exception('Моля въведете поне една дата (начална или крайна)');
        }

        $startDate = null;
        $endDate = null;

        // Валидация на началната дата, ако е въведена
        if ($hasStartDate) {
            $startDate = strtotime($post['promotion_date_start']);
            if ($startDate === false) {
                throw new \Exception('Невалиден формат на началната дата');
            }
            if ($startDate < strtotime('today')) {
                throw new \Exception('Началната дата не може да бъде в миналото');
            }
        }

        // Валидация на крайната дата, ако е въведена
        if ($hasEndDate) {
            $endDate = strtotime($post['promotion_date_end']);
            if ($endDate === false) {
                throw new \Exception('Невалиден формат на крайната дата');
            }
        }

        // Ако и двете дати са въведени, проверяваме реда им
        if ($hasStartDate && $hasEndDate && $startDate >= $endDate) {
            throw new \Exception('Крайната дата трябва да бъде след началната');
        }
        
        // Проверка дали има избрана категория или продукти
        $hasCategory = !empty($post['promotion_category_id']);
        $hasProducts = !empty($post['selected_products']) && is_array($post['selected_products']);

        if (!$hasCategory && !$hasProducts) {
            throw new \Exception('Моля изберете категория или конкретни продукти за промоцията');
        }

        $this->loadModelsAs([
                'catalog/product' => 'productModel',
                'catalog/category' => 'categoryModel'
            ]);

        // Валидация на категорията, ако е избрана
        if ($hasCategory) {    
            $category = $this->categoryModel->getCategory((int)$post['promotion_category_id']);
            if (!$category) {
                throw new \Exception('Избраната категория не съществува');
            }
        }

        // Валидация на продуктите, ако са избрани
        if ($hasProducts) {
            $productIds = $post['selected_products'];
            if (!is_array($productIds)) {
                throw new \Exception('Невалиден формат на продуктите');
            }

            foreach ($productIds as $productId) {
                $product = $this->productModel->getProduct((int)$productId);
                if (!$product) {
                    throw new \Exception('Един от избраните продукти не съществува');
                }
            }
        }
    }
    
    /**
     * Подготвя данните за промоцията
     *
     * @param array $post
     * @return array
     */
    private function preparePromotionData($post) {
        $data = [
            'value' => (float)$post['promotion_value'],
            'type' => $post['promotion_type'],
            'date_start' => !empty($post['promotion_date_start']) ? $post['promotion_date_start'] : null,
            'date_end' => !empty($post['promotion_date_end']) ? $post['promotion_date_end'] : null,
            'category_id' => !empty($post['promotion_category_id']) ? (int)$post['promotion_category_id'] : null,
            'product_ids' => !empty($post['selected_products']) ? $post['selected_products'] : [],
            'created_by' => $this->getUserId(),
            'created_at' => date('Y-m-d H:i:s')
        ];

        return $data;
    }
    
    /**
     * Запазва промоцията в базата данни
     *
     * @param array $data
     */
    private function savePromotion($data) {
        if (empty($data)) {
            throw new \Exception('Няма данни за запазване');
        }

        try {
            // Започваме транзакция
            $this->db->query("START TRANSACTION");

            // 1. Валидираме че таблицата product_special съществува
            $this->createPromotionRecord($data);

            // 2. Прилагане на промоцията към продуктите
            if ($data['category_id']) {
                // Прилагане към всички продукти в категорията
                $this->applyPromotionToCategory($data);
            }

            if (!empty($data['product_ids'])) {
                // Прилагане към конкретни продукти
                $this->applyPromotionToProducts($data);
            }

            // 3. Записваме промоцията в новата таблица promotions
            $this->savePromotionToPromotionsTable($data);

            // Потвърждаваме транзакцията
            $this->db->query("COMMIT");

            // Логиране на действието
            $this->logPromotionAction($data);

        } catch (\Exception $e) {
            // Отменяме транзакцията при грешка
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * Проверява дали таблицата product_special съществува и има правилната структура
     *
     * @param array $data
     */
    private function createPromotionRecord($data) {
        // Проверяваме дали съществува стандартната OpenCart таблица product_special
        $tableExists = $this->db->query("SHOW TABLES LIKE '" . DB_PREFIX . "product_special'");

        if ($tableExists->num_rows == 0) {
            throw new \Exception('Таблицата product_special не съществува в базата данни');
        }

        // Проверяваме структурата на таблицата
        if (!$this->validateProductSpecialTable()) {
            throw new \Exception('Таблицата product_special няма правилната структура');
        }

        // Логираме информация за промоцията (тъй като product_special не поддържа метаданни)
        $logData = [
            'type' => $data['type'],
            'value' => $data['value'],
            'date_start' => $data['date_start'],
            'date_end' => $data['date_end'],
            'category_id' => $data['category_id'],
            'product_ids' => $data['product_ids'],
            'created_by' => $data['created_by'],
            'created_at' => $data['created_at']
        ];

        F()->log->developer('Създаване на промоция: ' . json_encode($logData), __FILE__, __LINE__);
    }

    /**
     * Прилага промоцията към всички продукти в категорията (оптимизирано за големи обеми)
     *
     * @param array $data
     */
    private function applyPromotionToCategory($data) {
        // Получаваме всички продукти в категорията
        $sql = "SELECT p.product_id, p.price
                FROM `" . DB_PREFIX . "product` p
                LEFT JOIN `" . DB_PREFIX . "product_to_category` ptc ON (p.product_id = ptc.product_id)
                WHERE ptc.category_id = '{$data['category_id']}'
                AND p.status = '1'";

        $result = $this->db->query($sql);

        if ($result->num_rows == 0) {
            F()->log->developer("Няма активни продукти в категория {$data['category_id']}", __FILE__, __LINE__);
            return;
        }

        // Подготвяме списък с продукти за batch обработка
        $products = [];
        foreach ($result->rows as $product) {
            $products[] = [
                'product_id' => $product['product_id'],
                'price' => $product['price']
            ];
        }

        F()->log->developer("Намерени {$result->num_rows} продукта в категория {$data['category_id']} за промоция", __FILE__, __LINE__);

        // Прилагаме промоцията с batch обработка
        $this->applyPromotionBatch($products, $data);
    }

    /**
     * Прилага промоцията към конкретни продукти (оптимизирано за големи обеми)
     *
     * @param array $data
     */
    private function applyPromotionToProducts($data) {
        if (empty($data['product_ids'])) {
            return;
        }

        // Получаваме цените на всички продукти с една заявка
        $productIds = implode(',', array_map('intval', $data['product_ids']));
        $sql = "SELECT product_id, price FROM `" . DB_PREFIX . "product`
                WHERE product_id IN ({$productIds}) AND status = '1'";
        $result = $this->db->query($sql);

        if ($result->num_rows == 0) {
            F()->log->developer("Няма активни продукти от избраните за промоция", __FILE__, __LINE__);
            return;
        }

        // Подготвяме списък с продукти за batch обработка
        $products = [];
        foreach ($result->rows as $product) {
            $products[] = [
                'product_id' => $product['product_id'],
                'price' => $product['price']
            ];
        }

        F()->log->developer("Намерени {$result->num_rows} активни продукта от избраните за промоция", __FILE__, __LINE__);

        // Прилагаме промоцията с batch обработка
        $this->applyPromotionBatch($products, $data);
    }

    /**
     * Прилага промоцията към списък с продукти с batch обработка
     *
     * @param array $products Масив с продукти ['product_id' => id, 'price' => price]
     * @param array $data Данни за промоцията
     */
    private function applyPromotionBatch($products, $data) {
        $batchSize = 75; // Размер на порцията
        $totalProducts = count($products);
        $totalBatches = ceil($totalProducts / $batchSize);
        $successCount = 0;
        $errorCount = 0;

        F()->log->developer("Започва batch обработка на {$totalProducts} продукта в {$totalBatches} порции", __FILE__, __LINE__);

        // Получаваме съществуващи промоции за всички продукти
        $existingPromotions = $this->getExistingPromotions(array_column($products, 'product_id'));

        // Филтрираме продуктите - премахваме тези с припокриващи се промоции
        $filteredProducts = $this->filterProductsWithConflicts($products, $existingPromotions, $data);
        $filteredCount = count($filteredProducts);

        if ($filteredCount < $totalProducts) {
            $skippedCount = $totalProducts - $filteredCount;
            F()->log->developer("Пропуснати {$skippedCount} продукта поради припокриващи се промоции", __FILE__, __LINE__);
        }

        if ($filteredCount == 0) {
            F()->log->developer("Няма продукти за обработка след филтриране", __FILE__, __LINE__);
            return;
        }

        // Обработваме продуктите на порции
        $batches = array_chunk($filteredProducts, $batchSize);
        $currentBatch = 0;

        foreach ($batches as $batch) {
            $currentBatch++;

            try {
                $this->processBatch($batch, $data, $currentBatch, $totalBatches);
                $successCount += count($batch);

                // Пауза между порциите за да не претоварим сървъра
                if ($currentBatch < $totalBatches) {
                    sleep(1);
                }

            } catch (\Exception $e) {
                $errorCount += count($batch);
                F()->log->developer("Грешка при обработка на порция {$currentBatch}: " . $e->getMessage(), __FILE__, __LINE__);
            }
        }

        F()->log->developer("Завършена batch обработка: {$successCount} успешни, {$errorCount} неуспешни", __FILE__, __LINE__);
    }

    /**
     * Прилага промоцията към конкретен продукт (legacy метод за единични продукти)
     *
     * @param int $productId
     * @param float $originalPrice
     * @param array $data
     * @deprecated Използвай applyPromotionBatch() за по-добра производителност
     */
    private function applyPromotionToProduct($productId, $originalPrice, $data) {
        // Проверяваме за конфликти с съществуващи промоции
        $existingPromotions = $this->getExistingPromotions([$productId]);

        if (isset($existingPromotions[$productId])) {
            foreach ($existingPromotions[$productId] as $existing) {
                if ($this->hasDateOverlap($data['date_start'], $data['date_end'], $existing['date_start'], $existing['date_end'])) {
                    F()->log->developer("Пропуснат продукт {$productId} поради припокриваща се промоция", __FILE__, __LINE__);
                    return;
                }
            }
        }

        // Изчисляваме промоционалната цена
        if ($data['type'] === 'percentage') {
            $specialPrice = $originalPrice * (1 - $data['value'] / 100);
        } else {
            $specialPrice = max(0, $originalPrice - $data['value']);
        }

        // Форматираме датите според OpenCart стандарта
        $dateStart = $data['date_start'] ? $data['date_start'] : '0000-00-00';
        $dateEnd = $data['date_end'] ? $data['date_end'] : '0000-00-00';

        // Създаваме нов запис (старите конфликтни записи са проверени по-горе)
        $sql = "INSERT INTO `" . DB_PREFIX . "product_special` SET
                `product_id` = '{$productId}',
                `customer_group_id` = '1',
                `priority` = '1',
                `price` = '{$specialPrice}',
                `date_start` = '{$dateStart}',
                `date_end` = '{$dateEnd}'";

        // $this->db->query($sql);

        F()->log->developer("SQL заявката: " . $sql, __FILE__, __LINE__);

        // Логираме прилагането на промоцията
        F()->log->developer("Приложена промоция за продукт {$productId}: {$originalPrice} -> {$specialPrice}", __FILE__, __LINE__);
    }

    /**
     * Логира действието за промоция
     *
     * @param array $data
     */
    private function logPromotionAction($data) {
        $discountText = $data['type'] === 'percentage'
            ? $data['value'] . '% отстъпка'
            : $data['value'] . ' лв. отстъпка';

        // Формираме съобщението за периода
        $periodText = '';
        if ($data['date_start'] && $data['date_end']) {
            $periodText = "от {$data['date_start']} до {$data['date_end']}";
        } elseif ($data['date_start']) {
            $periodText = "от {$data['date_start']}";
        } elseif ($data['date_end']) {
            $periodText = "до {$data['date_end']}";
        }

        $logMessage = sprintf(
            'Приложена промоция: %s %s',
            $discountText,
            $periodText
        );

        if ($data['category_id']) {
            $logMessage .= ' за категория ID: ' . $data['category_id'];
        }

        if (!empty($data['product_ids'])) {
            $logMessage .= ' за продукти: ' . implode(', ', $data['product_ids']);
        }

        F()->log->developer($logMessage, __FILE__, __LINE__);
    }
    
    /**
     * Получава ID на текущия потребител
     *
     * @return int
     */
    private function getUserId() {
        // TODO: Имплементиране на получаване на потребителския ID
        return 1; // Временно
    }

    /**
     * Проверява структурата на таблицата product_special
     *
     * @return bool
     */
    private function validateProductSpecialTable() {
        $requiredColumns = ['product_special_id', 'product_id', 'customer_group_id', 'priority', 'price', 'date_start', 'date_end'];

        $columnsQuery = "SHOW COLUMNS FROM `" . DB_PREFIX . "product_special`";
        $result = $this->db->query($columnsQuery);

        $existingColumns = [];
        foreach ($result->rows as $row) {
            $existingColumns[] = $row['Field'];
        }

        foreach ($requiredColumns as $column) {
            if (!in_array($column, $existingColumns)) {
                F()->log->developer("Липсва колона '{$column}' в таблицата product_special", __FILE__, __LINE__);
                return false;
            }
        }

        return true;
    }

    /**
     * Получава съществуващи промоции за списък с продукти
     *
     * @param array $productIds
     * @return array
     */
    private function getExistingPromotions($productIds) {
        if (empty($productIds)) {
            return [];
        }

        $productIdsStr = implode(',', array_map('intval', $productIds));
        $sql = "SELECT product_id, date_start, date_end, price
                FROM `" . DB_PREFIX . "product_special`
                WHERE product_id IN ({$productIdsStr})
                AND customer_group_id = '1'";

        $result = $this->db->query($sql);

        $promotions = [];
        foreach ($result->rows as $row) {
            if (!isset($promotions[$row['product_id']])) {
                $promotions[$row['product_id']] = [];
            }
            $promotions[$row['product_id']][] = [
                'date_start' => $row['date_start'],
                'date_end' => $row['date_end'],
                'price' => $row['price']
            ];
        }

        return $promotions;
    }

    /**
     * Филтрира продукти с припокриващи се промоции
     *
     * @param array $products
     * @param array $existingPromotions
     * @param array $data
     * @return array
     */
    private function filterProductsWithConflicts($products, $existingPromotions, $data) {
        $filtered = [];

        foreach ($products as $product) {
            $productId = $product['product_id'];

            if (!isset($existingPromotions[$productId])) {
                // Няма съществуващи промоции за този продукт
                $filtered[] = $product;
                continue;
            }

            $hasConflict = false;
            foreach ($existingPromotions[$productId] as $existing) {
                if ($this->hasDateOverlap($data['date_start'], $data['date_end'], $existing['date_start'], $existing['date_end'])) {
                    $hasConflict = true;
                    break;
                }
            }

            if (!$hasConflict) {
                $filtered[] = $product;
            }
        }

        return $filtered;
    }

    /**
     * Проверява дали две промоции се припокриват по дати
     *
     * @param string $newStart
     * @param string $newEnd
     * @param string $existingStart
     * @param string $existingEnd
     * @return bool
     */
    private function hasDateOverlap($newStart, $newEnd, $existingStart, $existingEnd) {
        // Нормализираме датите
        $newStart = $newStart ?: '0000-00-00';
        $newEnd = $newEnd ?: '0000-00-00';
        $existingStart = $existingStart ?: '0000-00-00';
        $existingEnd = $existingEnd ?: '0000-00-00';

        // Ако някоя от промоциите е безкрайна (0000-00-00), има припокриване
        if ($newStart === '0000-00-00' && $newEnd === '0000-00-00') {
            return true; // Новата промоция е безкрайна
        }

        if ($existingStart === '0000-00-00' && $existingEnd === '0000-00-00') {
            return true; // Съществуващата промоция е безкрайна
        }

        // Проверяваме припокриване на дати
        if ($newStart !== '0000-00-00' && $existingEnd !== '0000-00-00' && $newStart > $existingEnd) {
            return false; // Новата започва след края на съществуващата
        }

        if ($newEnd !== '0000-00-00' && $existingStart !== '0000-00-00' && $newEnd < $existingStart) {
            return false; // Новата завършва преди началото на съществуващата
        }

        return true; // Има припокриване
    }

    /**
     * Обработва една порция продукти
     *
     * @param array $batch
     * @param array $data
     * @param int $currentBatch
     * @param int $totalBatches
     */
    private function processBatch($batch, $data, $currentBatch, $totalBatches) {
        $batchSize = count($batch);
        F()->log->developer("Обработва се порция {$currentBatch} от {$totalBatches} ({$batchSize} продукта)", __FILE__, __LINE__);

        // Подготвяме VALUES за batch INSERT
        $values = [];
        foreach ($batch as $product) {
            $productId = $product['product_id'];
            $originalPrice = $product['price'];

            // Изчисляваме промоционалната цена
            if ($data['type'] === 'percentage') {
                $specialPrice = $originalPrice * (1 - $data['value'] / 100);
            } else {
                $specialPrice = max(0, $originalPrice - $data['value']);
            }

            // Форматираме датите
            $dateStart = $data['date_start'] ? $data['date_start'] : '0000-00-00';
            $dateEnd = $data['date_end'] ? $data['date_end'] : '0000-00-00';

            $values[] = "({$productId}, 1, 1, {$specialPrice}, '{$dateStart}', '{$dateEnd}')";
        }

        // Изпълняваме batch INSERT
        $valuesStr = implode(',', $values);
        $sql = "INSERT INTO `" . DB_PREFIX . "product_special`
                (product_id, customer_group_id, priority, price, date_start, date_end)
                VALUES {$valuesStr}";

        // $this->db->query($sql);

        F()->log->developer("SQL заявката: " . $sql, __FILE__, __LINE__);

        F()->log->developer("Успешно обработена порция {$currentBatch} с {$batchSize} продукта", __FILE__, __LINE__);
    }

    /**
     * Създава таблицата за промоции ако не съществува
     */
    private function createPromotionsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "promotions` (
            `promotion_id` INT(11) NOT NULL AUTO_INCREMENT,
            `product_id` INT(11) NOT NULL,
            `discount_type` ENUM('percentage', 'fixed') NOT NULL,
            `discount_value` DECIMAL(10,2) NOT NULL,
            `start_date` DATE NOT NULL,
            `end_date` DATE NOT NULL,
            `status` TINYINT(1) DEFAULT 1,
            `date_added` DATETIME DEFAULT CURRENT_TIMESTAMP,
            `date_modified` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`promotion_id`),
            KEY `idx_product_id` (`product_id`),
            KEY `idx_status` (`status`),
            KEY `idx_dates` (`start_date`, `end_date`),
            KEY `idx_product_status` (`product_id`, `status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";

        try {
            $this->db->query($sql);
            F()->log->developer("Таблицата promotions е създадена или вече съществува", __FILE__, __LINE__);
        } catch (\Exception $e) {
            F()->log->developer("Грешка при създаване на таблицата promotions: " . $e->getMessage(), __FILE__, __LINE__);
        }
    }

    /**
     * Получава всички промоции с информация за продуктите
     *
     * @return array
     */
    private function getAllPromotions() {
        $sql = "SELECT
                    p.promotion_id,
                    p.product_id,
                    p.discount_type,
                    p.discount_value,
                    p.start_date,
                    p.end_date,
                    p.status,
                    p.date_added,
                    p.date_modified,
                    pd.name as product_name,
                    pr.model as product_model,
                    pr.price as product_price
                FROM `" . DB_PREFIX . "promotions` p
                LEFT JOIN `" . DB_PREFIX . "product` pr ON (p.product_id = pr.product_id)
                LEFT JOIN `" . DB_PREFIX . "product_description` pd ON (p.product_id = pd.product_id)
                WHERE pd.language_id = '{$this->getLanguageId()}'
                ORDER BY p.date_added DESC";

        try {
            $result = $this->db->query($sql);
            return $result->rows;
        } catch (\Exception $e) {
            F()->log->developer("Грешка при получаване на промоции: " . $e->getMessage(), __FILE__, __LINE__);
            return [];
        }
    }

    /**
     * Обработва AJAX заявки за редактиране на промоция
     */
    public function edit() {
        $json = [];

        try {
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $promotion_id = (int)$this->requestPost('promotion_id');
            if (!$promotion_id) {
                throw new \Exception('Невалиден ID на промоция');
            }

            // Валидация на входните данни
            $this->validatePromotionData($this->requestPost());

            // Подготвяне на данните за промоцията
            $promotionData = $this->preparePromotionData($this->requestPost());
            $promotionData['promotion_id'] = $promotion_id;

            // Обновяване на промоцията
            $this->updatePromotion($promotionData);

            $json['success'] = 'Промоцията беше успешно обновена!';

        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Обработва AJAX заявки за изтриване на промоция
     */
    public function delete() {
        $json = [];

        try {
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $promotion_id = (int)$this->requestPost('promotion_id');
            if (!$promotion_id) {
                throw new \Exception('Невалиден ID на промоция');
            }

            // Изтриване на промоцията
            $this->deletePromotion($promotion_id);

            $json['success'] = 'Промоцията беше успешно изтрита!';

        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Обработва AJAX заявки за промяна на статуса на промоция
     */
    public function toggleStatus() {
        $json = [];

        try {
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $promotion_id = (int)$this->requestPost('promotion_id');
            if (!$promotion_id) {
                throw new \Exception('Невалиден ID на промоция');
            }

            // Промяна на статуса
            $newStatus = $this->togglePromotionStatus($promotion_id);

            $json['success'] = $newStatus ? 'Промоцията беше активирана!' : 'Промоцията беше деактивирана!';
            $json['new_status'] = $newStatus;

        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Получава данни за конкретна промоция
     */
    public function getPromotion() {
        $json = [];

        try {
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $promotion_id = (int)$this->requestGet('promotion_id');
            if (!$promotion_id) {
                throw new \Exception('Невалиден ID на промоция');
            }

            $promotion = $this->getPromotionById($promotion_id);
            if (!$promotion) {
                throw new \Exception('Промоцията не е намерена');
            }

            $json['promotion'] = $promotion;

        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Обновява промоция в базата данни
     *
     * @param array $data
     */
    private function updatePromotion($data) {
        $sql = "UPDATE `" . DB_PREFIX . "promotions` SET
                `discount_type` = '{$data['type']}',
                `discount_value` = '{$data['value']}',
                `start_date` = '{$data['date_start']}',
                `end_date` = '{$data['date_end']}',
                `date_modified` = NOW()
                WHERE `promotion_id` = '{$data['promotion_id']}'";

        $this->db->query($sql);

        // Обновяваме и записа в product_special таблицата
        $this->updateProductSpecial($data);

        F()->log->developer("Обновена промоция с ID: {$data['promotion_id']}", __FILE__, __LINE__);
    }

    /**
     * Изтрива промоция от базата данни
     *
     * @param int $promotion_id
     */
    private function deletePromotion($promotion_id) {
        // Първо получаваме данните за промоцията
        $promotion = $this->getPromotionById($promotion_id);
        if (!$promotion) {
            throw new \Exception('Промоцията не е намерена');
        }

        // Изтриваме от promotions таблицата
        $sql = "DELETE FROM `" . DB_PREFIX . "promotions` WHERE `promotion_id` = '{$promotion_id}'";
        $this->db->query($sql);

        // Изтриваме съответните записи от product_special таблицата
        $this->deleteProductSpecial($promotion['product_id'], $promotion['start_date'], $promotion['end_date']);

        F()->log->developer("Изтрита промоция с ID: {$promotion_id}", __FILE__, __LINE__);
    }

    /**
     * Променя статуса на промоция
     *
     * @param int $promotion_id
     * @return int Новия статус
     */
    private function togglePromotionStatus($promotion_id) {
        // Получаваме текущия статус
        $sql = "SELECT `status` FROM `" . DB_PREFIX . "promotions` WHERE `promotion_id` = '{$promotion_id}'";
        $result = $this->db->query($sql);

        if ($result->num_rows == 0) {
            throw new \Exception('Промоцията не е намерена');
        }

        $currentStatus = (int)$result->row['status'];
        $newStatus = $currentStatus ? 0 : 1;

        // Обновяваме статуса
        $sql = "UPDATE `" . DB_PREFIX . "promotions` SET
                `status` = '{$newStatus}',
                `date_modified` = NOW()
                WHERE `promotion_id` = '{$promotion_id}'";

        $this->db->query($sql);

        F()->log->developer("Променен статус на промоция {$promotion_id}: {$currentStatus} -> {$newStatus}", __FILE__, __LINE__);

        return $newStatus;
    }

    /**
     * Получава промоция по ID
     *
     * @param int $promotion_id
     * @return array|false
     */
    private function getPromotionById($promotion_id) {
        $sql = "SELECT
                    p.*,
                    pd.name as product_name,
                    pr.model as product_model,
                    pr.price as product_price
                FROM `" . DB_PREFIX . "promotions` p
                LEFT JOIN `" . DB_PREFIX . "product` pr ON (p.product_id = pr.product_id)
                LEFT JOIN `" . DB_PREFIX . "product_description` pd ON (p.product_id = pd.product_id)
                WHERE p.promotion_id = '{$promotion_id}'
                AND pd.language_id = '{$this->getLanguageId()}'";

        $result = $this->db->query($sql);

        return $result->num_rows > 0 ? $result->row : false;
    }

    /**
     * Обновява записа в product_special таблицата
     *
     * @param array $data
     */
    private function updateProductSpecial($data) {
        // Първо изтриваме старите записи
        $this->deleteProductSpecial($data['product_id'], null, null);

        // Добавяме новия запис
        $this->addProductSpecial($data);
    }

    /**
     * Добавя запис в product_special таблицата
     *
     * @param array $data
     */
    private function addProductSpecial($data) {
        // Получаваме оригиналната цена на продукта
        $sql = "SELECT price FROM `" . DB_PREFIX . "product` WHERE product_id = '{$data['product_id']}'";
        $result = $this->db->query($sql);

        if ($result->num_rows == 0) {
            throw new \Exception('Продуктът не е намерен');
        }

        $originalPrice = $result->row['price'];

        // Изчисляваме промоционалната цена
        if ($data['type'] === 'percentage') {
            $specialPrice = $originalPrice * (1 - $data['value'] / 100);
        } else {
            $specialPrice = max(0, $originalPrice - $data['value']);
        }

        // Форматираме датите
        $dateStart = $data['date_start'] ? $data['date_start'] : '0000-00-00';
        $dateEnd = $data['date_end'] ? $data['date_end'] : '0000-00-00';

        // Добавяме записа
        $sql = "INSERT INTO `" . DB_PREFIX . "product_special` SET
                `product_id` = '{$data['product_id']}',
                `customer_group_id` = '1',
                `priority` = '1',
                `price` = '{$specialPrice}',
                `date_start` = '{$dateStart}',
                `date_end` = '{$dateEnd}'";

        $this->db->query($sql);
    }

    /**
     * Изтрива записи от product_special таблицата
     *
     * @param int $product_id
     * @param string|null $start_date
     * @param string|null $end_date
     */
    private function deleteProductSpecial($product_id, $start_date = null, $end_date = null) {
        $sql = "DELETE FROM `" . DB_PREFIX . "product_special` WHERE `product_id` = '{$product_id}'";

        if ($start_date && $end_date) {
            $sql .= " AND `date_start` = '{$start_date}' AND `date_end` = '{$end_date}'";
        }

        $this->db->query($sql);
    }

    /**
     * Записва промоцията в таблицата promotions
     *
     * @param array $data
     */
    private function savePromotionToPromotionsTable($data) {
        // Ако има категория, записваме промоция за всеки продукт в категорията
        if ($data['category_id']) {
            $this->savePromotionForCategory($data);
        }

        // Ако има конкретни продукти, записваме промоция за всеки от тях
        if (!empty($data['product_ids'])) {
            $this->savePromotionForProducts($data);
        }
    }

    /**
     * Записва промоции за всички продукти в категория
     *
     * @param array $data
     */
    private function savePromotionForCategory($data) {
        // Получаваме всички продукти в категорията
        $sql = "SELECT p.product_id
                FROM `" . DB_PREFIX . "product` p
                LEFT JOIN `" . DB_PREFIX . "product_to_category` ptc ON (p.product_id = ptc.product_id)
                WHERE ptc.category_id = '{$data['category_id']}'
                AND p.status = '1'";

        $result = $this->db->query($sql);

        foreach ($result->rows as $product) {
            $this->insertPromotionRecord($product['product_id'], $data);
        }

        F()->log->developer("Записани промоции за {$result->num_rows} продукта в категория {$data['category_id']}", __FILE__, __LINE__);
    }

    /**
     * Записва промоции за конкретни продукти
     *
     * @param array $data
     */
    private function savePromotionForProducts($data) {
        foreach ($data['product_ids'] as $product_id) {
            $this->insertPromotionRecord($product_id, $data);
        }

        F()->log->developer("Записани промоции за " . count($data['product_ids']) . " конкретни продукта", __FILE__, __LINE__);
    }

    /**
     * Вмъква запис за промоция в таблицата promotions
     *
     * @param int $product_id
     * @param array $data
     */
    private function insertPromotionRecord($product_id, $data) {
        $sql = "INSERT INTO `" . DB_PREFIX . "promotions` SET
                `product_id` = '{$product_id}',
                `discount_type` = '{$data['type']}',
                `discount_value` = '{$data['value']}',
                `start_date` = '{$data['date_start']}',
                `end_date` = '{$data['date_end']}',
                `status` = '1',
                `date_added` = NOW()";

        $this->db->query($sql);

        F()->log->developer("Записана промоция за продукт {$product_id}: {$data['type']} {$data['value']}", __FILE__, __LINE__);
    }
}
