<?php
// Тестов скрипт за проверка на функционалността за промоции
// Дата: 2025-07-08

// Включване на конфигурацията
require_once('admin/config.php');

// Създаване на връзка с базата данни
try {
    $pdo = new PDO("mysql:host=" . DB_HOSTNAME . ";port=" . DB_PORT . ";dbname=" . DB_DATABASE, DB_USERNAME, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("SET NAMES 'utf8'");
    
    echo "<h2>Тест на функционалността за промоции</h2>";
    
    // 1. Проверка дали таблицата promotions съществува
    echo "<h3>1. Проверка на таблицата promotions</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE '" . DB_PREFIX . "promotions'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Таблицата " . DB_PREFIX . "promotions съществува<br>";
        
        // Показваме структурата на таблицата
        $stmt = $pdo->query("DESCRIBE " . DB_PREFIX . "promotions");
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Поле</th><th>Тип</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ Таблицата " . DB_PREFIX . "promotions НЕ съществува<br>";
    }
    
    // 2. Проверка на таблицата product_special
    echo "<h3>2. Проверка на таблицата product_special</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE '" . DB_PREFIX . "product_special'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Таблицата " . DB_PREFIX . "product_special съществува<br>";
        
        // Броим записите
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM " . DB_PREFIX . "product_special");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "📊 Брой записи в product_special: " . $count . "<br>";
    } else {
        echo "❌ Таблицата " . DB_PREFIX . "product_special НЕ съществува<br>";
    }
    
    // 3. Проверка на продуктите в базата данни
    echo "<h3>3. Проверка на продуктите</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM " . DB_PREFIX . "product WHERE status = 1");
    $productCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "📊 Брой активни продукти: " . $productCount . "<br>";
    
    if ($productCount > 0) {
        // Показваме първите 5 продукта
        $stmt = $pdo->query("SELECT p.product_id, p.model, p.price, pd.name 
                            FROM " . DB_PREFIX . "product p 
                            LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id) 
                            WHERE p.status = 1 AND pd.language_id = 1 
                            LIMIT 5");
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Име</th><th>Модел</th><th>Цена</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . $row['product_id'] . "</td>";
            echo "<td>" . $row['name'] . "</td>";
            echo "<td>" . $row['model'] . "</td>";
            echo "<td>" . number_format($row['price'], 2) . " лв.</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 4. Проверка на категориите
    echo "<h3>4. Проверка на категориите</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM " . DB_PREFIX . "category WHERE status = 1");
    $categoryCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "📊 Брой активни категории: " . $categoryCount . "<br>";
    
    // 5. Проверка на промоциите (ако има)
    echo "<h3>5. Проверка на промоциите</h3>";
    if ($stmt = $pdo->query("SELECT COUNT(*) as count FROM " . DB_PREFIX . "promotions")) {
        $promotionCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "📊 Брой промоции: " . $promotionCount . "<br>";
        
        if ($promotionCount > 0) {
            // Показваме всички промоции
            $stmt = $pdo->query("SELECT p.*, pd.name as product_name, pr.model as product_model 
                                FROM " . DB_PREFIX . "promotions p 
                                LEFT JOIN " . DB_PREFIX . "product pr ON (p.product_id = pr.product_id)
                                LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                                WHERE pd.language_id = 1
                                ORDER BY p.date_added DESC");
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>Продукт</th><th>Тип</th><th>Стойност</th><th>Период</th><th>Статус</th><th>Създадена</th></tr>";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>" . $row['promotion_id'] . "</td>";
                echo "<td>" . $row['product_name'] . " (" . $row['product_model'] . ")</td>";
                echo "<td>" . ($row['discount_type'] == 'percentage' ? 'Процент' : 'Фиксирана') . "</td>";
                echo "<td>" . $row['discount_value'] . ($row['discount_type'] == 'percentage' ? '%' : ' лв.') . "</td>";
                echo "<td>" . $row['start_date'] . " - " . $row['end_date'] . "</td>";
                echo "<td>" . ($row['status'] ? 'Активна' : 'Неактивна') . "</td>";
                echo "<td>" . $row['date_added'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    echo "<h3>✅ Тестът завърши успешно!</h3>";
    echo "<p><a href='admin/index.php?route=catalog/product/promotion'>Отиди към страницата с промоции</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>❌ Грешка при свързване с базата данни:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<h3>❌ Обща грешка:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>
