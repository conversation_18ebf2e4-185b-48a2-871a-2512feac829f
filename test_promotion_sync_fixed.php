<?php
// Тестов скрипт за проверка на синхронизацията след поправките
// Дата: 2025-07-08

// Включване на конфигурацията
require_once('admin/config.php');

// Създаване на връзка с базата данни
try {
    $pdo = new PDO("mysql:host=" . DB_HOSTNAME . ";port=" . DB_PORT . ";dbname=" . DB_DATABASE, DB_USERNAME, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("SET NAMES 'utf8'");
    
    echo "<h2>Тест на синхронизацията след поправките</h2>";
    
    // 1. Проверяваме структурата на таблицата
    echo "<h3>1. Проверка на структурата на таблицата</h3>";
    $stmt = $pdo->query("DESCRIBE " . DB_PREFIX . "promotions");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasDataColumn = false;
    $hasProductIdColumn = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'data') {
            $hasDataColumn = true;
        }
        if ($column['Field'] === 'product_id') {
            $hasProductIdColumn = true;
        }
    }
    
    if ($hasDataColumn) {
        echo "✅ Колоната 'data' съществува<br>";
    } else {
        echo "❌ Колоната 'data' НЕ съществува<br>";
        exit;
    }
    
    if ($hasProductIdColumn) {
        echo "✅ Колоната 'product_id' съществува (за обратна съвместимост)<br>";
    }
    
    // 2. Получаваме тестови продукти
    echo "<h3>2. Подготовка на тестови данни</h3>";
    $stmt = $pdo->query("SELECT p.product_id, p.model, p.price, pd.name 
                        FROM " . DB_PREFIX . "product p 
                        LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id) 
                        WHERE p.status = 1 AND pd.language_id = 1 
                        LIMIT 3");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($products) < 2) {
        echo "❌ Няма достатъчно продукти за тест<br>";
        exit;
    }
    
    echo "✅ Намерени " . count($products) . " продукта за тест<br>";
    foreach ($products as $product) {
        echo "- Продукт ID: {$product['product_id']}, Име: {$product['name']}, Цена: {$product['price']} лв.<br>";
    }
    
    // 3. Симулираме създаване на промоция чрез контролера
    echo "<h3>3. Симулация на създаване на промоция</h3>";
    
    // Подготвяме данните като от формата
    $testProductIds = array_slice(array_column($products, 'product_id'), 0, 2);
    $testProductNames = array_slice(array_column($products, 'name'), 0, 2);
    
    $discountType = 'percentage';
    $discountValue = 20.00;
    $startDate = date('Y-m-d');
    $endDate = date('Y-m-d', strtotime('+30 days'));
    
    // Симулираме POST данни
    $_POST = [
        'promotion_type' => $discountType,
        'promotion_value' => $discountValue,
        'promotion_date_start' => $startDate,
        'promotion_date_end' => $endDate,
        'selected_products' => $testProductIds
    ];
    
    echo "Симулирани POST данни:<br>";
    echo "- Тип: {$discountType}<br>";
    echo "- Стойност: {$discountValue}%<br>";
    echo "- Период: {$startDate} - {$endDate}<br>";
    echo "- Продукти: " . implode(', ', $testProductIds) . "<br>";
    
    // 4. Създаваме промоция директно в базата данни с новата JSON структура
    echo "<h3>4. Създаване на промоция с JSON структура</h3>";
    
    $jsonData = [
        'category_id' => null,
        'product_ids' => array_map('intval', $testProductIds),
        'category_name' => null,
        'product_names' => $testProductNames
    ];
    
    $insertSql = "INSERT INTO `" . DB_PREFIX . "promotions` SET
                  `product_id` = NULL,
                  `data` = :data,
                  `discount_type` = :discount_type,
                  `discount_value` = :discount_value,
                  `start_date` = :start_date,
                  `end_date` = :end_date,
                  `status` = 1";
    
    $stmt = $pdo->prepare($insertSql);
    $stmt->execute([
        ':data' => json_encode($jsonData),
        ':discount_type' => $discountType,
        ':discount_value' => $discountValue,
        ':start_date' => $startDate,
        ':end_date' => $endDate
    ]);
    
    $testPromotionId = $pdo->lastInsertId();
    echo "✅ Създадена промоция с ID: {$testPromotionId}<br>";
    echo "JSON данни: <pre>" . json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    
    // 5. Симулираме синхронизацията с product_special
    echo "<h3>5. Симулация на синхронизацията с product_special</h3>";
    
    $syncedCount = 0;
    foreach ($testProductIds as $productId) {
        // Получаваме оригиналната цена
        $stmt = $pdo->prepare("SELECT price FROM " . DB_PREFIX . "product WHERE product_id = ?");
        $stmt->execute([$productId]);
        $productPrice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($productPrice) {
            $originalPrice = $productPrice['price'];
            $specialPrice = $originalPrice * (1 - $discountValue / 100);
            
            // Добавяме в product_special
            $specialSql = "INSERT INTO `" . DB_PREFIX . "product_special` SET
                          `product_id` = :product_id,
                          `customer_group_id` = 1,
                          `priority` = 1,
                          `price` = :price,
                          `date_start` = :date_start,
                          `date_end` = :date_end";
            
            $stmt = $pdo->prepare($specialSql);
            $stmt->execute([
                ':product_id' => $productId,
                ':price' => $specialPrice,
                ':date_start' => $startDate,
                ':date_end' => $endDate
            ]);
            
            $syncedCount++;
            echo "✅ Синхронизиран продукт {$productId}: {$originalPrice} -> {$specialPrice} лв.<br>";
        }
    }
    
    echo "✅ Синхронизирани {$syncedCount} продукта в product_special<br>";
    
    // 6. Проверяваме записите в product_special
    echo "<h3>6. Проверка на записите в product_special</h3>";
    $productIdsStr = implode(',', $testProductIds);
    $stmt = $pdo->query("SELECT ps.*, pd.name as product_name 
                        FROM " . DB_PREFIX . "product_special ps
                        LEFT JOIN " . DB_PREFIX . "product_description pd ON (ps.product_id = pd.product_id)
                        WHERE ps.product_id IN ({$productIdsStr}) 
                        AND ps.date_start = '{$startDate}' 
                        AND ps.date_end = '{$endDate}'
                        AND pd.language_id = 1");
    $specialRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Намерени " . count($specialRecords) . " записа в product_special:<br>";
    if (count($specialRecords) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Продукт</th><th>Оригинална цена</th><th>Промоционална цена</th><th>Период</th></tr>";
        foreach ($specialRecords as $record) {
            // Получаваме оригиналната цена
            $stmt = $pdo->prepare("SELECT price FROM " . DB_PREFIX . "product WHERE product_id = ?");
            $stmt->execute([$record['product_id']]);
            $originalPrice = $stmt->fetch(PDO::FETCH_ASSOC)['price'];
            
            echo "<tr>";
            echo "<td>" . $record['product_name'] . " (ID: " . $record['product_id'] . ")</td>";
            echo "<td>" . number_format($originalPrice, 2) . " лв.</td>";
            echo "<td>" . number_format($record['price'], 2) . " лв.</td>";
            echo "<td>" . $record['date_start'] . " - " . $record['date_end'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 7. Тестваме четенето на промоцията
    echo "<h3>7. Тест на четенето на промоцията</h3>";
    $stmt = $pdo->prepare("SELECT * FROM " . DB_PREFIX . "promotions WHERE promotion_id = ?");
    $stmt->execute([$testPromotionId]);
    $promotion = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($promotion) {
        echo "✅ Промоцията е прочетена успешно<br>";
        
        $decodedData = json_decode($promotion['data'], true);
        if ($decodedData) {
            echo "✅ JSON данните са декодирани успешно<br>";
            echo "<ul>";
            echo "<li>Категория ID: " . ($decodedData['category_id'] ?? 'null') . "</li>";
            echo "<li>Продукти: " . count($decodedData['product_ids']) . "</li>";
            echo "<li>Имена на продукти: " . implode(', ', $decodedData['product_names']) . "</li>";
            echo "</ul>";
            
            // Проверяваме дали product_ids съвпадат с тези в product_special
            $promotionProductIds = $decodedData['product_ids'];
            $specialProductIds = array_column($specialRecords, 'product_id');
            
            if (array_diff($promotionProductIds, $specialProductIds) == [] && 
                array_diff($specialProductIds, $promotionProductIds) == []) {
                echo "✅ Продуктите в promotions и product_special са синхронизирани<br>";
            } else {
                echo "❌ Продуктите в promotions и product_special НЕ са синхронизирани<br>";
                echo "Promotions: " . implode(', ', $promotionProductIds) . "<br>";
                echo "Product_special: " . implode(', ', $specialProductIds) . "<br>";
            }
        } else {
            echo "❌ Грешка при декодиране на JSON данните<br>";
        }
    } else {
        echo "❌ Промоцията не е намерена<br>";
    }
    
    // Опция за изчистване
    echo "<br><h4>Изчистване на тестовите данни:</h4>";
    echo "<p><a href='?cleanup={$testPromotionId}' style='color: red;'>Изтрий тестовата промоция</a></p>";
    
    // Ако е поискано изчистване
    if (isset($_GET['cleanup']) && $_GET['cleanup'] == $testPromotionId) {
        echo "<h3>Изчистване на тестовите данни...</h3>";
        
        // Изтриваме промоцията
        $stmt = $pdo->prepare("DELETE FROM " . DB_PREFIX . "promotions WHERE promotion_id = ?");
        $stmt->execute([$testPromotionId]);
        echo "✅ Изтрита промоция от promotions таблицата<br>";
        
        // Изтриваме от product_special
        $stmt = $pdo->prepare("DELETE FROM " . DB_PREFIX . "product_special 
                              WHERE product_id IN ({$productIdsStr}) 
                              AND date_start = ? AND date_end = ?");
        $stmt->execute([$startDate, $endDate]);
        echo "✅ Изтрити записи от product_special таблицата<br>";
        
        echo "<p>Тестовите данни са изчистени. <a href='?'>Обнови страницата</a></p>";
    }
    
    echo "<br><h3>✅ Тестът на синхронизацията завърши успешно!</h3>";
    echo "<p><a href='admin/index.php?route=catalog/product/promotion'>Отидете към страницата с промоции</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>❌ Грешка при работа с базата данни:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<h3>❌ Обща грешка:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>
