/**
 * Category Form JavaScript Module
 * Управлява функционалността на формата за категории
 */
(function() {
    'use strict';

    console.log('[CategoryForm] Module loading...');

    // Helper for Cyrillic to Latin transliteration
    const cyrillicToLatinMap = {
        'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ж': 'zh', 'з': 'z', 'и': 'i',
        'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm', 'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's',
        'т': 't', 'у': 'u', 'ф': 'f', 'х': 'h', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sht', 'ъ': 'a',
        'ь': 'y', 'ю': 'yu', 'я': 'ya'
    };

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        // Проверяваме дали BackendModule е зареден
        if (typeof BackendModule !== 'undefined') {
            console.log('[CategoryForm] BackendModule found, initializing category form...');
            BackendModule.initCategoryForm();
        } else {
            console.error('[CategoryForm] BackendModule not found! Make sure backend.js is loaded first.');
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
        Object.assign(BackendModule, {
            /**
             * Инициализация на модула за категория форма
             */
            initCategoryForm: function() {
                this.initCategoryFormValidation();
                this.initCategoryFormRichTextEditors();
                this.initParentCategoryAutocomplete();
                this.initCategoryFormSubmission();
                this.initCategoryFormTabNavigation();
                this.bindCategoryFormEvents();

                this.logDev && this.logDev('CategoryForm initialized successfully');
            },

            /**
             * Инициализация на валидацията на формата
             */
            initCategoryFormValidation: function() {
                const form = document.getElementById('form-category');
                if (!form) return;

                // Валидация при изпращане на формата
                form.addEventListener('submit', (e) => {
                    if (!this.validateCategoryForm()) {
                        e.preventDefault();
                        return false;
                    }
                });

                // Валидация в реално време
                this.initCategoryFormRealTimeValidation();
            },

            /**
             * Инициализация на валидация в реално време
             */
            initCategoryFormRealTimeValidation: function() {
                // Валидация на имената на категориите
                const nameInputs = document.querySelectorAll('input[name*="[name]"]');
                nameInputs.forEach(input => {
                    input.addEventListener('blur', () => {
                        this.validateCategoryFormName(input);
                    });
                });

                // Валидация на SEO URL-та
                const seoInputs = document.querySelectorAll('input[name*="category_seo_url"]');
                seoInputs.forEach(input => {
                    input.addEventListener('blur', () => {
                        this.validateCategoryFormSeoUrl(input);
                    });
                });

                // Валидация на позицията
                const sortOrderInput = document.getElementById('input-sort-order');
                if (sortOrderInput) {
                    sortOrderInput.addEventListener('blur', () => {
                        this.validateCategoryFormSortOrder(sortOrderInput);
                    });
                }
            },

            /**
             * Валидация на цялата форма
             */
            validateCategoryForm: function() {
                let isValid = true;
                const errors = [];

                // Проверка дали има поне едно име на категория
                const nameInputs = document.querySelectorAll('input[name*="[name]"]');
                let hasName = false;

                nameInputs.forEach(input => {
                    if (input.value.trim()) {
                        hasName = true;
                    }
                });

                if (!hasName) {
                    errors.push('Моля въведете име на категорията поне за един език');
                    isValid = false;
                }

                // Валидация на SEO URL-та
                const seoInputs = document.querySelectorAll('input[name*="category_seo_url"]');
                seoInputs.forEach(input => {
                    if (!this.validateCategoryFormSeoUrl(input)) {
                        isValid = false;
                    }
                });

                // Показване на грешките
                if (!isValid) {
                    this.showCategoryFormValidationErrors(errors);
                }

                return isValid;
            },

            /**
             * Валидация на име на категория
             */
            validateCategoryFormName: function(input) {
                const value = input.value.trim();
                const isValid = value.length > 0 && value.length <= 255;

                this.toggleCategoryFormInputValidation(input, isValid,
                    isValid ? '' : 'Името на категорията трябва да бъде между 1 и 255 символа');

                return isValid;
            },

            /**
             * Валидация на SEO URL
             */
            validateCategoryFormSeoUrl: function(input) {
                const value = input.value.trim();

                if (!value) {
                    this.toggleCategoryFormInputValidation(input, true, '');
                    return true;
                }

                // Проверка за валиден SEO URL формат
                const seoUrlPattern = /^[a-zа-я0-9\-_\/]+$/;
                const isValid = seoUrlPattern.test(value) && value.length <= 255;

                this.toggleCategoryFormInputValidation(input, isValid,
                    isValid ? '' : 'SEO URL може да съдържа само малки букви, цифри, тирета и долни черти');

                return isValid;
            },

            /**
             * Валидация на позиция
             */
            validateCategoryFormSortOrder: function(input) {
                const value = input.value.trim();

                if (!value) {
                    this.toggleCategoryFormInputValidation(input, true, '');
                    return true;
                }

                const numValue = parseInt(value);
                const isValid = !isNaN(numValue) && numValue >= 0;

                this.toggleCategoryFormInputValidation(input, isValid,
                    isValid ? '' : 'Позицията трябва да бъде положително число');

                return isValid;
            },

            /**
             * Превключва визуалната валидация на поле
             */
            toggleCategoryFormInputValidation: function(input, isValid, errorMessage) {
                const feedback = input.parentNode.querySelector('.invalid-feedback');

                if (isValid) {
                    input.classList.remove('is-invalid');
                    input.classList.add('is-valid');
                    if (feedback) feedback.textContent = '';
                } else {
                    input.classList.remove('is-valid');
                    input.classList.add('is-invalid');
                    if (feedback) feedback.textContent = errorMessage;
                }
            },

            /**
             * Показва грешки от валидацията
             */
            showCategoryFormValidationErrors: function(errors) {
                if (errors.length === 0) return;

                const errorHtml = errors.map(error => `<li>${error}</li>`).join('');

                this.showAlert('danger', `
                    <strong>Грешки във формата:</strong>
                    <ul class="mb-0 mt-2">${errorHtml}</ul>
                `);
            },

            /**
             * Инициализация на rich text редакторите
             */
            initCategoryFormRichTextEditors: function() {
                if (typeof AdvancedRichTextEditor !== 'undefined') {
                    const editors = AdvancedRichTextEditor.initializeAll('.editor-container', {
                        height: 300,
                        autoSync: true,
                        plugins: ['tables', 'images', 'links'],
                        toolbar: 'full',
                        contextMenu: true,
                    });
                    window.richTextEditors = editors;
                } else {
                    console.error('AdvancedRichTextEditor не е зареден!');
                }
            },

            /**
             * Инициализация и управление на автодовършването за родителска категория
             * Адаптиран от initCategoryAutocomplete в product-form.js
             */
            initParentCategoryAutocomplete: function() {
                // Loading indicator за autocomplete
                let loadingIndicator = document.createElement('div');
                const parentCategoryInput = document.getElementById('input-parent-category');
                const parentCategorySuggestions = document.getElementById('category-parent-autocomplete');
                const parentCategoryContainer = document.getElementById('category-parent');
                const parentIdInput = document.getElementById('input-parent-id');

                if (!parentCategoryInput || !parentCategorySuggestions || !parentCategoryContainer || !parentIdInput) {
                    console.warn('Елементи за автодовършване на родителска категория не са намерени.');
                    return;
                }

                // Проверяваме дали вече има event listeners
                if (parentCategoryInput.dataset.listenerAttached === 'true') {
                    return;
                }

                loadingIndicator.className = 'autocomplete-loading p-2 text-gray-500 text-sm';
                loadingIndicator.textContent = 'Зареждане...';
                loadingIndicator.style.display = 'none';
                parentCategorySuggestions.appendChild(loadingIndicator);

                let categoryDebounceTimer;
                let currentCategoryRequest = null;

                const replaceParentCategory = (categoryName, categoryId) => {
                    // Изчистваме контейнера за родителска категория
                    parentCategoryContainer.innerHTML = '';

                    // Създаваме нов елемент за избраната родителска категория
                    const categoryElement = document.createElement('div');
                    categoryElement.className = 'flex items-center justify-between p-2 bg-gray-100 rounded';
                    categoryElement.id = `category-parent${categoryId}`;
                    categoryElement.innerHTML = `
                        <span class="text-sm">${categoryName}</span>
                        <button type="button" class="text-gray-400 hover:text-red-500 remove-parent-category">
                            <i class="ri-close-line"></i>
                        </button>
                    `;

                    const removeButton = categoryElement.querySelector('.remove-parent-category');
                    removeButton.addEventListener('click', () => {
                        parentCategoryContainer.innerHTML = '';
                        parentIdInput.value = '0';
                        parentCategoryInput.focus();
                    });

                    // Добавяме елемента в правилния контейнер
                    parentCategoryContainer.appendChild(categoryElement);

                    // Задаваме стойността на скритото поле
                    parentIdInput.value = categoryId;

                    // Скриваме предложенията
                    parentCategorySuggestions.innerHTML = '';
                    parentCategorySuggestions.classList.add('hidden');
                    loadingIndicator.style.display = 'none';
                };

                const fetchAndDisplayCategorySuggestions = (currentQuery) => {
                    loadingIndicator.style.display = 'block';
                    parentCategorySuggestions.classList.add('hidden');

                    if (currentCategoryRequest) {
                        currentCategoryRequest.abort();
                    }
                    const controller = new AbortController();
                    currentCategoryRequest = controller;

                    const urlParams = new URLSearchParams(window.location.search);
                    const userToken = urlParams.get('user_token');
                    const excludeCategoryId = urlParams.get('category_id') || 0;
                    const excludeParentId = parentIdInput.value || 0;
                    const timestamp = new Date().getTime();

                    // Използваме същия endpoint като в оригиналния метод с добавен exclude_parent_id
                    let fetchUrl = `index.php?route=catalog/category/autocomplete&filter_name=${encodeURIComponent(currentQuery)}&exclude_category_id=${excludeCategoryId}&user_token=${userToken}&_=${timestamp}`;

                    // Добавяме exclude_parent_id ако има избрана родителска категория
                    if (excludeParentId && excludeParentId != '0') {
                        fetchUrl += `&exclude_parent_id=${excludeParentId}`;
                    }

                    fetch(fetchUrl, {
                        signal: controller.signal,
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                    .then(response => {
                        if (!response.ok) throw new Error('Грешка при зареждане на категории');
                        return response.json();
                    })
                    .then(data => {
                        loadingIndicator.style.display = 'none';

                        // Създаваме контейнер за предложенията с правилните стилове
                        parentCategorySuggestions.innerHTML = '';
                        const suggestionsList = document.createElement('div');
                        suggestionsList.className = 'absolute w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 z-10 max-h-60 overflow-y-auto';

                        if (data.length > 0) {
                            data.forEach(category => {
                                const item = document.createElement('div');
                                item.className = 'p-2 cursor-pointer hover:bg-gray-100 autocomplete-suggestion';
                                item.textContent = category.name;
                                item.dataset.id = category.category_id;
                                item.addEventListener('click', () => {
                                    replaceParentCategory(category.name, category.category_id);
                                });
                                suggestionsList.appendChild(item);
                            });
                        } else {
                            const noResults = document.createElement('div');
                            noResults.className = 'p-2 text-gray-500 autocomplete-no-results';
                            noResults.textContent = currentQuery ? 'Няма намерени категории' : 'Няма предложения за категории';
                            suggestionsList.appendChild(noResults);
                        }

                        parentCategorySuggestions.appendChild(suggestionsList);
                        parentCategorySuggestions.classList.remove('hidden');
                    })
                    .catch(error => {
                        loadingIndicator.style.display = 'none';
                        if (error.name !== 'AbortError') {
                            console.error('Грешка при търсене на категории:', error);
                            parentCategorySuggestions.innerHTML = '<div class="p-2 text-red-500">Грешка при зареждане.</div>';
                            parentCategorySuggestions.classList.remove('hidden');
                        }
                    });
                };

                parentCategoryInput.addEventListener('input', () => {
                    clearTimeout(categoryDebounceTimer);
                    const query = parentCategoryInput.value.trim();

                    if (query.length === 0) {
                        // При празно поле показваме първите 10 категории
                        categoryDebounceTimer = setTimeout(() => {
                            fetchAndDisplayCategorySuggestions('');
                        }, 100);
                    } else {
                        // При въведен текст търсим по въведената дума
                        categoryDebounceTimer = setTimeout(() => {
                            fetchAndDisplayCategorySuggestions(query);
                        }, 500);
                    }
                });

                parentCategoryInput.addEventListener('focus', () => {
                    const query = parentCategoryInput.value.trim();
                    // При фокус винаги показваме предложения
                    if (query === '') {
                        // Ако полето е празно, показваме първите 10 категории
                        fetchAndDisplayCategorySuggestions('');
                    } else {
                        // Ако има текст, показваме резултати за този текст
                        fetchAndDisplayCategorySuggestions(query);
                    }
                });

                parentCategoryInput.addEventListener('keydown', (e) => {
                    const suggestions = parentCategorySuggestions.querySelectorAll('.autocomplete-suggestion');
                    if (suggestions.length === 0 || parentCategorySuggestions.classList.contains('hidden')) return;

                    let activeIndex = Array.from(suggestions).findIndex(s => s.classList.contains('active'));

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex + 1) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex - 1 + suggestions.length) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'Enter' && activeIndex >= 0) {
                        e.preventDefault();
                        suggestions[activeIndex].click();
                    } else if (e.key === 'Escape') {
                        parentCategorySuggestions.innerHTML = '';
                        parentCategorySuggestions.classList.add('hidden');
                    }
                });

                document.addEventListener('click', function(event) {
                    if (!parentCategoryInput.contains(event.target) && !parentCategorySuggestions.contains(event.target)) {
                        parentCategorySuggestions.classList.add('hidden');
                    }
                });

                // Обработка на съществуващи избрани категории при зареждане
                const existingSelected = parentCategoryContainer.querySelector('.flex.items-center');
                if (existingSelected) {
                    const removeButton = existingSelected.querySelector('.remove-parent-category');
                    if (removeButton) {
                        removeButton.addEventListener('click', () => {
                            parentCategoryContainer.innerHTML = '';
                            parentIdInput.value = '0';
                            parentCategoryInput.focus();
                        });
                    }
                }

                // Маркираме, че event listeners са добавени
                parentCategoryInput.dataset.listenerAttached = 'true';
            },

            /**
             * Генерира SEO URL от текст
             */
            generateSeoUrl: function(text) {
                return text.trim()
                    .toLowerCase()
                    .replace(/[а-я]/g, match => cyrillicToLatinMap[match] || '')
                    .replace(/[^a-z0-9\-_]/g, '-')
                    .replace(/-+/g, '-')
                    .replace(/^-|-$/g, '');
            },
            
            /**
             * Инициализация на изпращането на формата
             */
            initCategoryFormSubmission: function() {
                const form = document.getElementById('form-category');
                if (!form) return;

                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.submitCategoryForm();
                });
            },

            /**
             * Изпраща формата чрез AJAX
             */
            submitCategoryForm: function() {
                const form = document.getElementById('form-category');
                if (!form) return;

                // Показване на loading индикатор
                this.showCategoryFormLoadingState(true);

                const formData = new FormData(form);

                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    this.handleCategoryFormResponse(data);
                })
                .catch(error => {
                    console.error('Грешка при изпращане на формата:', error);
                    this.showAlert('danger', 'Възникна грешка при запазване на категорията');
                })
                .finally(() => {
                    this.showCategoryFormLoadingState(false);
                });
            },

            /**
             * Обработва отговора от сървъра
             */
            handleCategoryFormResponse: function(data) {

                console.log(data);


                if (data.error) {
                    this.showAlert('danger', data.error);
                } else if (data.success) {
                    this.showAlert('success', data.success);



                    // Пренасочване, ако е указано
                    // if (data.redirect) {
                    //     setTimeout(() => {
                    //         window.location.href = data.redirect;
                    //     }, 1500);
                    // }
                }
            },

            /**
             * Показва/скрива loading състояние
             */
            showCategoryFormLoadingState: function(show) {
                const submitBtn = document.querySelector('button[type="submit"][form="form-category"]');

                if (submitBtn) {
                    if (show) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> Запазване...';
                    } else {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = '<div class="w-5 h-5 flex items-center justify-center mr-2"><i class="ri-save-line"></i></div><span>Запази</span>';
                    }
                }
            },

            /**
             * Инициализация на навигацията между табовете
             */
            initCategoryFormTabNavigation: function() {
                const tabButtons = document.querySelectorAll('.tab-button[data-tab]');
                const tabContents = document.querySelectorAll('.tab-content[id]');

                const showTab = (tabId) => {
                    const targetButton = document.querySelector(`.tab-button[data-tab="${tabId}"]`);
                    const targetContent = document.getElementById(tabId);

                    if (!targetButton || !targetContent) return;

                    // Актуализиране на бутоните
                    tabButtons.forEach(button => {
                        button.classList.remove('active');
                        button.classList.add('text-gray-500');
                    });
                    targetButton.classList.add('active');
                    targetButton.classList.remove('text-gray-500');

                    // Актуализиране на съдържанието
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    targetContent.classList.remove('hidden');
                };

                // Добавяне на събитие при клик
                tabButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const tabId = this.getAttribute('data-tab');
                        if (history.pushState) {
                            const newUrl = window.location.pathname + window.location.search + '#' + tabId;
                            history.pushState(null, null, newUrl);
                        } else {
                            window.location.hash = tabId;
                        }
                        showTab(tabId);
                    });
                });

                // Показване на таб при зареждане на страницата от URL hash
                if (window.location.hash) {
                    const tabId = window.location.hash.substring(1);
                    showTab(tabId);
                    // Предотвратяване на "скачането" на страницата
                    setTimeout(() => {
                        window.scrollTo(0, 0);
                    }, 1);
                }
            },

            /**
             * Свързва събитията за формата
             */
            bindCategoryFormEvents: function() {
                document.body.addEventListener('click', (e) => {
                    const target = e.target;

                    // Generate SEO URL
                    const generateSeoBtn = target.closest('[data-action="generate-seo-url"]');
                    if (generateSeoBtn) {
                        e.preventDefault();
                        const languageId = generateSeoBtn.dataset.languageId;
                        const nameInput = document.getElementById(`input-name-${languageId}`);
                        const seoInput = document.getElementById(`input-seo-url-${languageId}`);
                        if (nameInput && seoInput && nameInput.value.trim()) {
                            seoInput.value = this.generateSeoUrl(nameInput.value);
                        }
                    }

                    // Parent Category Add
                    const addParentBtn = target.closest('.add-parent-category');
                    if (addParentBtn) {
                        e.preventDefault();
                        const categoryId = addParentBtn.dataset.categoryId;
                        const categoryName = addParentBtn.dataset.categoryName;
                        const parentCategoryContainer = document.getElementById('category-parent');
                        const parentIdInput = document.getElementById('input-parent-id');
                        const parentCategoryInput = document.getElementById('input-parent-category');
                        const parentCategoryAutocomplete = document.getElementById('category-parent-autocomplete');

                        parentCategoryContainer.innerHTML = `
                            <div id="category-parent${categoryId}" class="flex items-center justify-between p-2 bg-gray-100 rounded">
                                <span class="text-sm">${categoryName}</span>
                                <button type="button" class="text-gray-400 hover:text-red-500 remove-parent-category">
                                    <i class="ri-close-line"></i>
                                </button>
                            </div>`;
                        parentIdInput.value = categoryId;
                        parentCategoryInput.value = '';
                        parentCategoryAutocomplete.innerHTML = '';
                    }

                    // Parent Category Remove
                    const removeParentBtn = target.closest('.remove-parent-category');
                    if (removeParentBtn) {
                        e.preventDefault();
                        document.getElementById('category-parent').innerHTML = '';
                        document.getElementById('input-parent-id').value = '0';
                    }

                    // Image Manager: Open from library
                    const openLibraryBtn = target.closest('[data-action="open-category-image-library"], [data-action="select-category-image"]');
                    if (openLibraryBtn) {
                        e.preventDefault();
                        const targetContainer = document.getElementById('category-image-container');

                        BackendModule.openImageManager({
                            singleSelection: true,
                            startDirectory: this.getCategoryImageDirectory(), // Подаваме текущата директория
                            target: targetContainer,
                            callback: (selectedImages, target) => {

                                console.log(selectedImages);

                                if (selectedImages && selectedImages.length > 0) {
                                    const image = selectedImages[0];

                                    console.log(image);

                                    this.setCategoryImage(image.path, image.thumb);
                                }
                            }
                        });
                    }

                    // Image Manager: Remove image
                    const removeImageBtn = target.closest('[data-action="remove-category-image"]');
                    if (removeImageBtn) {
                        e.preventDefault();
                        this.setCategoryImage('', window.placeholder);
                    }
                });

                // Image Manager: Upload via file input
                const fileInput = document.querySelector('input[name="category_image"]');
                if (fileInput) {
                    fileInput.addEventListener('change', (e) => {
                        if (e.target.files && e.target.files[0]) {
                            const file = e.target.files[0];

                            // Проверяваме дали методът за качване е наличен
                            if (!BackendModule.uploadSingleFile || typeof BackendModule.uploadSingleFile !== 'function') {
                                console.error('image-manager.js не е зареден или uploadSingleFile методът не е наличен');
                                this.showAlert('error', 'Функционалността за качване не е налична');
                                return;
                            }

                            BackendModule.uploadSingleFile(file)
                                .then(result => {
                                    if (result && result.success) {
                                        // В product-form.js се използват result.filename и result.thumb
                                        // setCategoryImage очаква imagePath и thumbPath
                                        // Приемаме, че result.path е еквивалент на result.filename
                                        this.setCategoryImage(result.path || result.filename, result.thumb);
                                        this.showAlert('success', 'Изображението е качено успешно!');
                                    } else {
                                        this.showAlert('error', result.error || 'Грешка при качване на изображение');
                                    }
                                })
                                .catch(error => {
                                    console.error('Грешка при качване на изображение:', error);
                                    this.showAlert('error', 'Възникна грешка при качване на изображението');
                                });
                        }
                    });
                }
            },

            /**
             * Задава изображение на категорията и актуализира UI
             */
            setCategoryImage: function(imagePath, thumbPath) {
                const imageInput = document.getElementById('input-image');
                const previewContainer = document.getElementById('category-image-container');

                if (!imageInput || !previewContainer) {
                    console.error('Missing image input or preview container for category form.');
                    return;
                }

                imageInput.value = imagePath || '';
                let displayThumb = thumbPath || window.placeholder;


                if (imagePath) {

                    if(!displayThumb){
                        displayThumb = imagePath;
                    }

                    previewContainer.innerHTML = `
                        <div class="relative group">
                            <img src="${displayThumb}" alt="" id="thumb-image" class="rounded-lg object-cover w-full h-auto" width="192" height="192" />
                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                <button type="button" data-action="select-category-image" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Промяна">
                                    <div class="w-5 h-5 flex items-center justify-center"><i class="ri-pencil-line"></i></div>
                                </button>
                                <button type="button" data-action="remove-category-image" class="p-2 bg-white rounded-full text-red-500 hover:text-red-600" title="Премахване">
                                    <div class="w-5 h-5 flex items-center justify-center"><i class="ri-delete-bin-line"></i></div>
                                </button>
                            </div>
                        </div>`;
                } else {
                    previewContainer.innerHTML = `
                        <div class="border border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors" style="width: 192px; height: 192px;">
                        <div class="flex items-center space-x-2 mb-2">
                            <button type="button" class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-primary transition-colors" onclick="document.querySelector('input[name=category_image]').click()" title="Качи нов файл">
                                <i class="ri-add-line ri-lg"></i>
                            </button>
                            <button type="button" data-action="open-category-image-library" class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-primary transition-colors" title="Избери от библиотеката">
                                <i class="ri-folder-image-line ri-lg"></i>
                            </button>
                        </div>
                        <p class="text-xs text-gray-400 text-center">Няма изображение</p>
                    </div>`;
                }
            },

            /**
             * Връща директорията на текущото изображение на категорията
             */
            getCategoryImageDirectory: function() {
                const imageInput = document.getElementById('input-image');
                if (!imageInput || !imageInput.value) {
                    return '';
                }

                const pathParts = imageInput.value.split('/');
                if (pathParts.length > 1) {
                    pathParts.pop(); // remove filename
                    // Вече не премахваме 'catalog' префикса, тъй като използваме getImageServerPath()
                    return pathParts.join('/');
                }
                return '';
            },
        });

        console.log('[CategoryForm] BackendModule extended successfully');
    } else {
        console.error('[CategoryForm] BackendModule not available for extension');
    }

    console.log('[CategoryForm] Module loaded');
})();
